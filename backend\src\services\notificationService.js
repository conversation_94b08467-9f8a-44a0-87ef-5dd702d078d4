const nodemailer = require('nodemailer');
const twilio = require('twilio');
const admin = require('firebase-admin');

// إعداد Twilio للرسائل النصية
let twilioClient;
if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
  twilioClient = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
}

// إعداد Nodemailer للبريد الإلكتروني
let emailTransporter;
if (process.env.EMAIL_HOST && process.env.EMAIL_USER && process.env.EMAIL_PASSWORD) {
  emailTransporter = nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT || 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD
    }
  });
}

// إعداد Firebase للإشعارات الفورية
let firebaseApp;
if (process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_PRIVATE_KEY) {
  try {
    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: process.env.FIREBASE_AUTH_URI,
      token_uri: process.env.FIREBASE_TOKEN_URI
    };

    firebaseApp = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID
    });
  } catch (error) {
    console.error('خطأ في إعداد Firebase:', error);
  }
}

// إرسال رسالة نصية
const sendSMS = async (phoneNumber, message) => {
  if (!twilioClient) {
    console.warn('Twilio غير مكون، لا يمكن إرسال SMS');
    return { success: false, error: 'SMS service not configured' };
  }

  try {
    const result = await twilioClient.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phoneNumber
    });

    console.log(`تم إرسال SMS إلى ${phoneNumber}: ${result.sid}`);
    return { success: true, messageId: result.sid };
  } catch (error) {
    console.error('خطأ في إرسال SMS:', error);
    throw error;
  }
};

// إرسال بريد إلكتروني
const sendEmail = async (to, subject, text, html = null) => {
  if (!emailTransporter) {
    console.warn('Email transporter غير مكون، لا يمكن إرسال البريد الإلكتروني');
    return { success: false, error: 'Email service not configured' };
  }

  try {
    const mailOptions = {
      from: `"منصة المواطن" <${process.env.EMAIL_USER}>`,
      to,
      subject,
      text,
      html: html || `<p>${text}</p>`
    };

    const result = await emailTransporter.sendMail(mailOptions);
    console.log(`تم إرسال بريد إلكتروني إلى ${to}: ${result.messageId}`);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('خطأ في إرسال البريد الإلكتروني:', error);
    throw error;
  }
};

// إرسال إشعار فوري (Push Notification)
const sendPushNotification = async (fcmToken, title, body, data = {}) => {
  if (!firebaseApp) {
    console.warn('Firebase غير مكون، لا يمكن إرسال Push Notification');
    return { success: false, error: 'Push notification service not configured' };
  }

  try {
    const message = {
      notification: {
        title,
        body
      },
      data: {
        ...data,
        timestamp: new Date().toISOString()
      },
      token: fcmToken
    };

    const result = await admin.messaging().send(message);
    console.log(`تم إرسال Push Notification: ${result}`);
    return { success: true, messageId: result };
  } catch (error) {
    console.error('خطأ في إرسال Push Notification:', error);
    throw error;
  }
};

// إرسال إشعار جماعي
const sendBulkPushNotification = async (fcmTokens, title, body, data = {}) => {
  if (!firebaseApp || !fcmTokens || fcmTokens.length === 0) {
    console.warn('Firebase غير مكون أو لا توجد رموز FCM');
    return { success: false, error: 'Push notification service not configured or no tokens' };
  }

  try {
    const message = {
      notification: {
        title,
        body
      },
      data: {
        ...data,
        timestamp: new Date().toISOString()
      },
      tokens: fcmTokens
    };

    const result = await admin.messaging().sendMulticast(message);
    console.log(`تم إرسال ${result.successCount} إشعار من أصل ${fcmTokens.length}`);
    
    if (result.failureCount > 0) {
      console.warn(`فشل في إرسال ${result.failureCount} إشعار`);
      result.responses.forEach((resp, idx) => {
        if (!resp.success) {
          console.error(`خطأ في الرمز ${fcmTokens[idx]}: ${resp.error}`);
        }
      });
    }

    return { 
      success: true, 
      successCount: result.successCount,
      failureCount: result.failureCount,
      responses: result.responses
    };
  } catch (error) {
    console.error('خطأ في إرسال الإشعارات الجماعية:', error);
    throw error;
  }
};

// إرسال إشعار متعدد الوسائط
const sendMultiChannelNotification = async (notification, user) => {
  const results = {
    push: { success: false },
    email: { success: false },
    sms: { success: false }
  };

  const { delivery_methods } = notification;

  // إرسال Push Notification
  if (delivery_methods.push && user.fcm_token) {
    try {
      results.push = await sendPushNotification(
        user.fcm_token,
        notification.title,
        notification.message,
        {
          notification_id: notification.id,
          type: notification.type,
          action_url: notification.action_url || ''
        }
      );
    } catch (error) {
      console.error('خطأ في إرسال Push Notification:', error);
      results.push = { success: false, error: error.message };
    }
  }

  // إرسال البريد الإلكتروني
  if (delivery_methods.email && user.email) {
    try {
      const emailHtml = `
        <div dir="rtl" style="font-family: Arial, sans-serif;">
          <h2>${notification.title}</h2>
          <p>${notification.message}</p>
          ${notification.action_url ? `<a href="${notification.action_url}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">${notification.action_text || 'عرض التفاصيل'}</a>` : ''}
          <hr>
          <p style="color: #666; font-size: 12px;">
            هذه رسالة تلقائية من منصة المواطن. يرجى عدم الرد على هذا البريد الإلكتروني.
          </p>
        </div>
      `;

      results.email = await sendEmail(
        user.email,
        notification.title,
        notification.message,
        emailHtml
      );
    } catch (error) {
      console.error('خطأ في إرسال البريد الإلكتروني:', error);
      results.email = { success: false, error: error.message };
    }
  }

  // إرسال SMS
  if (delivery_methods.sms && user.phone_number) {
    try {
      const smsMessage = `${notification.title}\n\n${notification.message}${notification.action_url ? `\n\nالرابط: ${notification.action_url}` : ''}`;
      results.sms = await sendSMS(user.phone_number, smsMessage);
    } catch (error) {
      console.error('خطأ في إرسال SMS:', error);
      results.sms = { success: false, error: error.message };
    }
  }

  return results;
};

// اختبار الاتصال بالخدمات
const testServices = async () => {
  const status = {
    sms: false,
    email: false,
    push: false
  };

  // اختبار SMS
  if (twilioClient) {
    try {
      await twilioClient.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
      status.sms = true;
    } catch (error) {
      console.error('خطأ في اختبار Twilio:', error);
    }
  }

  // اختبار Email
  if (emailTransporter) {
    try {
      await emailTransporter.verify();
      status.email = true;
    } catch (error) {
      console.error('خطأ في اختبار Email:', error);
    }
  }

  // اختبار Firebase
  if (firebaseApp) {
    try {
      await admin.messaging().send({
        topic: 'test',
        notification: { title: 'Test', body: 'Test' }
      }, true); // dry run
      status.push = true;
    } catch (error) {
      if (error.code !== 'messaging/invalid-argument') {
        console.error('خطأ في اختبار Firebase:', error);
      } else {
        status.push = true; // dry run نجح
      }
    }
  }

  return status;
};

module.exports = {
  sendSMS,
  sendEmail,
  sendPushNotification,
  sendBulkPushNotification,
  sendMultiChannelNotification,
  testServices
};
