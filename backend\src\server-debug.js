const express = require('express');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// إعداد الوسطاء الأساسية
app.use(express.json());

// مسار الجذر
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في منصة المواطن - Citizen Platform API',
    version: '1.0.0',
    status: 'working'
  });
});

// مسار الصحة
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString()
  });
});

console.log('🔍 بدء اختبار استيراد النماذج والمسارات...');

try {
  console.log('📊 استيراد النماذج...');
  const { testConnection, syncDatabase, seedDatabase } = require('./models');
  console.log('✅ تم استيراد النماذج بنجاح');
} catch (error) {
  console.error('❌ خطأ في استيراد النماذج:', error.message);
}

try {
  console.log('📁 استيراد مسار auth...');
  const authRoutes = require('./routes/auth');
  app.use('/api/auth', authRoutes);
  console.log('✅ تم استيراد مسار auth بنجاح');
} catch (error) {
  console.error('❌ خطأ في استيراد مسار auth:', error.message);
}

try {
  console.log('📁 استيراد مسار users...');
  const userRoutes = require('./routes/users');
  app.use('/api/users', userRoutes);
  console.log('✅ تم استيراد مسار users بنجاح');
} catch (error) {
  console.error('❌ خطأ في استيراد مسار users:', error.message);
}

try {
  console.log('📁 استيراد مسار requests...');
  const requestRoutes = require('./routes/requests');
  app.use('/api/requests', requestRoutes);
  console.log('✅ تم استيراد مسار requests بنجاح');
} catch (error) {
  console.error('❌ خطأ في استيراد مسار requests:', error.message);
}

try {
  console.log('📁 استيراد مسار images...');
  const imageRoutes = require('./routes/images');
  app.use('/api/images', imageRoutes);
  console.log('✅ تم استيراد مسار images بنجاح');
} catch (error) {
  console.error('❌ خطأ في استيراد مسار images:', error.message);
}

try {
  console.log('📁 استيراد مسار notifications...');
  const notificationRoutes = require('./routes/notifications');
  app.use('/api/notifications', notificationRoutes);
  console.log('✅ تم استيراد مسار notifications بنجاح');
} catch (error) {
  console.error('❌ خطأ في استيراد مسار notifications:', error.message);
}

try {
  console.log('📁 استيراد مسار reports...');
  const reportRoutes = require('./routes/reports');
  app.use('/api/reports', reportRoutes);
  console.log('✅ تم استيراد مسار reports بنجاح');
} catch (error) {
  console.error('❌ خطأ في استيراد مسار reports:', error.message);
}

// بدء الخادم
app.listen(PORT, () => {
  console.log(`🎉 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الرابط: http://localhost:${PORT}`);
});

module.exports = app;
