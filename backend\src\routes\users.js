const express = require('express');
const { body, query } = require('express-validator');
const userController = require('../controllers/userController');
const { authenticate, adminOnly, employeeOnly } = require('../middleware/auth');
const { uploadSingle, processImage } = require('../middleware/upload');

const router = express.Router();

// جميع المسارات تتطلب مصادقة
router.use(authenticate);

// الحصول على قائمة المستخدمين (للإدارة والموظفين فقط)
router.get('/', employeeOnly, [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('نص البحث يجب أن يكون حرفين على الأقل'),
  query('user_type')
    .optional()
    .isIn(['citizen', 'employee', 'admin'])
    .withMessage('نوع المستخدم غير صحيح'),
  query('is_active')
    .optional()
    .isBoolean()
    .withMessage('حالة النشاط يجب أن تكون true أو false')
], userController.getUsers);

// الحصول على مستخدم محدد
router.get('/:id', userController.getUserById);

// إنشاء مستخدم جديد (للإدارة فقط)
router.post('/', adminOnly, [
  body('full_name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم الكامل يجب أن يكون بين 2 و 100 حرف'),
  body('phone_number')
    .isMobilePhone('ar-SA')
    .withMessage('رقم الهاتف غير صحيح'),
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  body('user_type')
    .isIn(['citizen', 'employee', 'admin'])
    .withMessage('نوع المستخدم غير صحيح'),
  body('password')
    .if(body('user_type').isIn(['employee', 'admin']))
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص'),
  body('national_id')
    .optional()
    .isLength({ min: 10, max: 10 })
    .withMessage('رقم الهوية يجب أن يكون 10 أرقام')
], userController.createUser);

// تحديث مستخدم
router.put('/:id', [
  body('full_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم الكامل يجب أن يكون بين 2 و 100 حرف'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  body('address')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('العنوان يجب أن يكون أقل من 500 حرف'),
  body('date_of_birth')
    .optional()
    .isISO8601()
    .withMessage('تاريخ الميلاد غير صحيح')
], userController.updateUser);

// حذف مستخدم (للإدارة فقط)
router.delete('/:id', adminOnly, userController.deleteUser);

// تفعيل/إلغاء تفعيل مستخدم (للإدارة فقط)
router.patch('/:id/toggle-status', adminOnly, userController.toggleUserStatus);

// رفع صورة الملف الشخصي
router.post('/:id/profile-image', 
  uploadSingle('profile_image'),
  processImage,
  userController.uploadProfileImage
);

// حذف صورة الملف الشخصي
router.delete('/:id/profile-image', userController.deleteProfileImage);

// الحصول على إحصائيات المستخدمين (للإدارة فقط)
router.get('/stats/overview', adminOnly, userController.getUserStats);

// الحصول على طلبات المستخدم
router.get('/:id/requests', [
  query('status')
    .optional()
    .isIn(['pending', 'under_review', 'in_progress', 'completed', 'rejected', 'cancelled'])
    .withMessage('حالة الطلب غير صحيحة'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 50')
], userController.getUserRequests);

// الحصول على إشعارات المستخدم
router.get('/:id/notifications', [
  query('is_read')
    .optional()
    .isBoolean()
    .withMessage('حالة القراءة يجب أن تكون true أو false'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 50')
], userController.getUserNotifications);

module.exports = router;
