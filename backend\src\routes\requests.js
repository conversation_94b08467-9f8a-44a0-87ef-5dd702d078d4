const express = require('express');
const { body, query } = require('express-validator');
const requestController = require('../controllers/requestController');
const { 
  authenticate, 
  citizenOnly, 
  employeeOnly, 
  canAccessRequest,
  requirePhoneVerification 
} = require('../middleware/auth');
const { requestCreationLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// جميع المسارات تتطلب مصادقة
router.use(authenticate);

// الحصول على قائمة الطلبات
router.get('/', [
  query('status')
    .optional()
    .isIn(['pending', 'under_review', 'in_progress', 'completed', 'rejected', 'cancelled'])
    .withMessage('حالة الطلب غير صحيحة'),
  query('request_type')
    .optional()
    .isIn([
      'birth_certificate', 'death_certificate', 'marriage_certificate',
      'divorce_certificate', 'residence_certificate', 'income_certificate',
      'employment_certificate', 'student_certificate', 'health_certificate',
      'police_clearance', 'commercial_license', 'building_permit', 'other'
    ])
    .withMessage('نوع الطلب غير صحيح'),
  query('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('أولوية الطلب غير صحيحة'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 50'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('نص البحث يجب أن يكون حرفين على الأقل'),
  query('date_from')
    .optional()
    .isISO8601()
    .withMessage('تاريخ البداية غير صحيح'),
  query('date_to')
    .optional()
    .isISO8601()
    .withMessage('تاريخ النهاية غير صحيح')
], requestController.getRequests);

// إنشاء طلب جديد (للمواطنين فقط)
router.post('/', citizenOnly, requirePhoneVerification, requestCreationLimiter, [
  body('request_type')
    .isIn([
      'birth_certificate', 'death_certificate', 'marriage_certificate',
      'divorce_certificate', 'residence_certificate', 'income_certificate',
      'employment_certificate', 'student_certificate', 'health_certificate',
      'police_clearance', 'commercial_license', 'building_permit', 'other'
    ])
    .withMessage('نوع الطلب غير صحيح'),
  body('title')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('عنوان الطلب يجب أن يكون بين 5 و 200 حرف'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('وصف الطلب يجب أن يكون بين 10 و 2000 حرف'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('أولوية الطلب غير صحيحة'),
  body('additional_data')
    .optional()
    .isObject()
    .withMessage('البيانات الإضافية يجب أن تكون كائن JSON')
], requestController.createRequest);

// الحصول على طلب محدد
router.get('/:id', canAccessRequest, requestController.getRequestById);

// تحديث طلب (للمواطن صاحب الطلب فقط)
router.put('/:id', canAccessRequest, [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('عنوان الطلب يجب أن يكون بين 5 و 200 حرف'),
  body('description')
    .optional()
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('وصف الطلب يجب أن يكون بين 10 و 2000 حرف'),
  body('additional_data')
    .optional()
    .isObject()
    .withMessage('البيانات الإضافية يجب أن تكون كائن JSON')
], requestController.updateRequest);

// حذف طلب (للمواطن صاحب الطلب فقط)
router.delete('/:id', canAccessRequest, requestController.deleteRequest);

// تحديث حالة الطلب (للموظفين والإدارة فقط)
router.patch('/:id/status', canAccessRequest, employeeOnly, [
  body('status')
    .isIn(['pending', 'under_review', 'in_progress', 'completed', 'rejected', 'cancelled'])
    .withMessage('حالة الطلب غير صحيحة'),
  body('employee_notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('ملاحظات الموظف يجب أن تكون أقل من 1000 حرف'),
  body('rejection_reason')
    .if(body('status').equals('rejected'))
    .notEmpty()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('سبب الرفض مطلوب ويجب أن يكون بين 10 و 500 حرف'),
  body('expected_completion_date')
    .optional()
    .isISO8601()
    .withMessage('تاريخ الإنجاز المتوقع غير صحيح')
], requestController.updateRequestStatus);

// إسناد طلب إلى موظف (للإدارة فقط)
router.patch('/:id/assign', canAccessRequest, employeeOnly, [
  body('assigned_to')
    .isUUID()
    .withMessage('معرف الموظف غير صحيح')
], requestController.assignRequest);

// إلغاء إسناد طلب (للإدارة فقط)
router.patch('/:id/unassign', canAccessRequest, employeeOnly, requestController.unassignRequest);

// تقييم الطلب (للمواطن صاحب الطلب فقط)
router.post('/:id/rating', canAccessRequest, [
  body('citizen_rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('التقييم يجب أن يكون بين 1 و 5'),
  body('citizen_feedback')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('التعليق يجب أن يكون أقل من 1000 حرف')
], requestController.rateRequest);

// الحصول على إحصائيات الطلبات
router.get('/stats/overview', employeeOnly, [
  query('period')
    .optional()
    .isIn(['today', 'week', 'month', 'quarter', 'year'])
    .withMessage('فترة الإحصائيات غير صحيحة'),
  query('date_from')
    .optional()
    .isISO8601()
    .withMessage('تاريخ البداية غير صحيح'),
  query('date_to')
    .optional()
    .isISO8601()
    .withMessage('تاريخ النهاية غير صحيح')
], requestController.getRequestStats);

// الحصول على الطلبات المسندة للموظف الحالي
router.get('/assigned/me', employeeOnly, [
  query('status')
    .optional()
    .isIn(['pending', 'under_review', 'in_progress', 'completed', 'rejected', 'cancelled'])
    .withMessage('حالة الطلب غير صحيحة'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 50')
], requestController.getAssignedRequests);

module.exports = router;
