const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

// استيراد قاعدة البيانات والنماذج
const { testConnection } = require('./models');

// استيراد المسارات
const authRoutes = require('./routes/auth');
const imageRoutes = require('./routes/images');

// استيراد الوسطاء
const { generalLimiter } = require('./middleware/rateLimiter');

const app = express();
const PORT = process.env.PORT || 5000;

// إعداد الوسطاء الأساسية
app.use(helmet());
app.use(cors());
app.use(express.json());

// مسار الجذر
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في منصة المواطن - Citizen Platform API',
    version: '1.0.0',
    status: 'working'
  });
});

// مسار الصحة
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString()
  });
});

// بدء الخادم
app.listen(PORT, () => {
  console.log(`🎉 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الرابط: http://localhost:${PORT}`);
});

module.exports = app;
