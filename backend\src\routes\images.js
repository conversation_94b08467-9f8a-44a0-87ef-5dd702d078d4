const express = require('express');
const { body, query } = require('express-validator');
const imageController = require('../controllers/imageController');
const { 
  authenticate, 
  canAccessRequest, 
  employeeOnly 
} = require('../middleware/auth');
const { 
  uploadMultiple, 
  processImage, 
  handleUploadError,
  uploadLimiter 
} = require('../middleware/upload');

const router = express.Router();

// جميع المسارات تتطلب مصادقة
router.use(authenticate);

// رفع صور/مرفقات لطلب محدد
router.post('/upload/:requestId', 
  canAccessRequest,
  uploadLimiter,
  uploadMultiple('files', 10),
  processImage,
  [
    body('attachment_types')
      .optional()
      .isArray()
      .withMessage('أنواع المرفقات يجب أن تكون مصفوفة'),
    body('attachment_types.*')
      .optional()
      .isIn([
        'identity_document', 'proof_of_residence', 'income_proof',
        'medical_report', 'academic_certificate', 'employment_letter',
        'marriage_certificate', 'birth_certificate', 'death_certificate',
        'court_document', 'bank_statement', 'utility_bill',
        'passport_copy', 'driving_license', 'other'
      ])
      .withMessage('نوع المرفق غير صحيح'),
    body('descriptions')
      .optional()
      .isArray()
      .withMessage('أوصاف المرفقات يجب أن تكون مصفوفة'),
    body('descriptions.*')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('وصف المرفق يجب أن يكون أقل من 500 حرف'),
    body('is_required')
      .optional()
      .isArray()
      .withMessage('حالة الإلزام يجب أن تكون مصفوفة'),
    body('is_required.*')
      .optional()
      .isBoolean()
      .withMessage('حالة الإلزام يجب أن تكون true أو false')
  ],
  handleUploadError,
  imageController.uploadImages
);

// الحصول على صور/مرفقات طلب محدد
router.get('/request/:requestId', canAccessRequest, [
  query('attachment_type')
    .optional()
    .isIn([
      'identity_document', 'proof_of_residence', 'income_proof',
      'medical_report', 'academic_certificate', 'employment_letter',
      'marriage_certificate', 'birth_certificate', 'death_certificate',
      'court_document', 'bank_statement', 'utility_bill',
      'passport_copy', 'driving_license', 'other'
    ])
    .withMessage('نوع المرفق غير صحيح'),
  query('verification_status')
    .optional()
    .isIn(['pending', 'verified', 'rejected'])
    .withMessage('حالة التحقق غير صحيحة')
], imageController.getRequestImages);

// الحصول على صورة/مرفق محدد
router.get('/:id', imageController.getImageById);

// تحديث معلومات صورة/مرفق
router.put('/:id', [
  body('attachment_type')
    .optional()
    .isIn([
      'identity_document', 'proof_of_residence', 'income_proof',
      'medical_report', 'academic_certificate', 'employment_letter',
      'marriage_certificate', 'birth_certificate', 'death_certificate',
      'court_document', 'bank_statement', 'utility_bill',
      'passport_copy', 'driving_license', 'other'
    ])
    .withMessage('نوع المرفق غير صحيح'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('وصف المرفق يجب أن يكون أقل من 500 حرف'),
  body('is_required')
    .optional()
    .isBoolean()
    .withMessage('حالة الإلزام يجب أن تكون true أو false')
], imageController.updateImage);

// حذف صورة/مرفق
router.delete('/:id', imageController.deleteImage);

// تحميل صورة/مرفق
router.get('/:id/download', imageController.downloadImage);

// عرض صورة/مرفق في المتصفح
router.get('/:id/view', imageController.viewImage);

// التحقق من صورة/مرفق (للموظفين والإدارة فقط)
router.patch('/:id/verify', employeeOnly, [
  body('verification_status')
    .isIn(['verified', 'rejected'])
    .withMessage('حالة التحقق يجب أن تكون verified أو rejected'),
  body('verification_notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('ملاحظات التحقق يجب أن تكون أقل من 1000 حرف')
], imageController.verifyImage);

// الحصول على إحصائيات الصور/المرفقات
router.get('/stats/overview', employeeOnly, [
  query('period')
    .optional()
    .isIn(['today', 'week', 'month', 'quarter', 'year'])
    .withMessage('فترة الإحصائيات غير صحيحة'),
  query('attachment_type')
    .optional()
    .isIn([
      'identity_document', 'proof_of_residence', 'income_proof',
      'medical_report', 'academic_certificate', 'employment_letter',
      'marriage_certificate', 'birth_certificate', 'death_certificate',
      'court_document', 'bank_statement', 'utility_bill',
      'passport_copy', 'driving_license', 'other'
    ])
    .withMessage('نوع المرفق غير صحيح')
], imageController.getImageStats);

// البحث في الصور/المرفقات
router.get('/search/files', employeeOnly, [
  query('q')
    .trim()
    .isLength({ min: 2 })
    .withMessage('نص البحث يجب أن يكون حرفين على الأقل'),
  query('attachment_type')
    .optional()
    .isIn([
      'identity_document', 'proof_of_residence', 'income_proof',
      'medical_report', 'academic_certificate', 'employment_letter',
      'marriage_certificate', 'birth_certificate', 'death_certificate',
      'court_document', 'bank_statement', 'utility_bill',
      'passport_copy', 'driving_license', 'other'
    ])
    .withMessage('نوع المرفق غير صحيح'),
  query('verification_status')
    .optional()
    .isIn(['pending', 'verified', 'rejected'])
    .withMessage('حالة التحقق غير صحيحة'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 50')
], imageController.searchImages);

module.exports = router;
