const { validationResult } = require('express-validator');
const { Image, Request } = require('../models');
const { createError, asyncHandler } = require('../middleware/errorHandler');

// رفع صور/مرفقات لطلب محدد
const uploadImages = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق رفع الصور لاحقاً',
    data: { images: [] }
  });
});

// الحصول على صور/مرفقات طلب محدد
const getRequestImages = asyncHandler(async (req, res) => {
  const { requestId } = req.params;
  const { attachment_type, verification_status } = req.query;

  const where = { request_id: requestId };

  if (attachment_type) {
    where.attachment_type = attachment_type;
  }

  if (verification_status) {
    where.verification_status = verification_status;
  }

  const images = await Image.findAll({
    where,
    order: [['created_at', 'DESC']]
  });

  res.json({
    success: true,
    data: { images }
  });
});

// الحصول على صورة/مرفق محدد
const getImageById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const image = await Image.findByPk(id, {
    include: [
      {
        model: Request,
        as: 'request',
        attributes: ['id', 'request_number', 'citizen_id']
      }
    ]
  });

  if (!image) {
    throw createError(404, 'الصورة غير موجودة', 'غير موجود');
  }

  res.json({
    success: true,
    data: { image }
  });
});

// تحديث معلومات صورة/مرفق
const updateImage = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { id } = req.params;
  const { attachment_type, description, is_required } = req.body;

  const image = await Image.findByPk(id);
  if (!image) {
    throw createError(404, 'الصورة غير موجودة', 'غير موجود');
  }

  // تحديث البيانات
  if (attachment_type) image.attachment_type = attachment_type;
  if (description !== undefined) image.description = description;
  if (is_required !== undefined) image.is_required = is_required;

  await image.save();

  res.json({
    success: true,
    message: 'تم تحديث معلومات الصورة بنجاح',
    data: { image }
  });
});

// حذف صورة/مرفق
const deleteImage = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const image = await Image.findByPk(id);
  if (!image) {
    throw createError(404, 'الصورة غير موجودة', 'غير موجود');
  }

  await image.destroy();

  res.json({
    success: true,
    message: 'تم حذف الصورة بنجاح'
  });
});

// تحميل صورة/مرفق
const downloadImage = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق تحميل الصور لاحقاً'
  });
});

// عرض صورة/مرفق في المتصفح
const viewImage = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق عرض الصور لاحقاً'
  });
});

// التحقق من صورة/مرفق
const verifyImage = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { id } = req.params;
  const { verification_status, verification_notes } = req.body;

  const image = await Image.findByPk(id);
  if (!image) {
    throw createError(404, 'الصورة غير موجودة', 'غير موجود');
  }

  // تحديث حالة التحقق
  image.verification_status = verification_status;
  image.verification_notes = verification_notes;
  image.verified_by = req.user.id;
  image.verified_at = new Date();

  await image.save();

  res.json({
    success: true,
    message: 'تم تحديث حالة التحقق بنجاح',
    data: { image }
  });
});

// الحصول على إحصائيات الصور/المرفقات
const getImageStats = asyncHandler(async (req, res) => {
  const totalImages = await Image.count();
  const pendingImages = await Image.count({ where: { verification_status: 'pending' } });
  const verifiedImages = await Image.count({ where: { verification_status: 'verified' } });
  const rejectedImages = await Image.count({ where: { verification_status: 'rejected' } });

  res.json({
    success: true,
    data: {
      total_images: totalImages,
      pending_images: pendingImages,
      verified_images: verifiedImages,
      rejected_images: rejectedImages
    }
  });
});

// البحث في الصور/المرفقات
const searchImages = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق البحث في الصور لاحقاً',
    data: { images: [] }
  });
});

module.exports = {
  uploadImages,
  getRequestImages,
  getImageById,
  updateImage,
  deleteImage,
  downloadImage,
  viewImage,
  verifyImage,
  getImageStats,
  searchImages
};
