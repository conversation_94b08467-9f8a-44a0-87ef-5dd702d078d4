const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Notification = sequelize.define('Notification', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  
  // معرف المستخدم المستلم
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  // معرف الطلب المرتبط (اختياري)
  request_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'requests',
      key: 'id'
    }
  },
  
  // نوع الإشعار
  type: {
    type: DataTypes.ENUM(
      'request_submitted',      // تم تقديم الطلب
      'request_received',       // تم استلام الطلب
      'request_under_review',   // الطلب قيد المراجعة
      'request_in_progress',    // الطلب قيد التنفيذ
      'request_completed',      // تم إنجاز الطلب
      'request_rejected',       // تم رفض الطلب
      'request_cancelled',      // تم إلغاء الطلب
      'document_required',      // مطلوب مستند إضافي
      'document_verified',      // تم التحقق من المستند
      'document_rejected',      // تم رفض المستند
      'appointment_scheduled',  // تم تحديد موعد
      'appointment_reminder',   // تذكير بالموعد
      'payment_required',       // مطلوب دفع رسوم
      'payment_received',       // تم استلام الدفع
      'system_maintenance',     // صيانة النظام
      'general_announcement',   // إعلان عام
      'other'                   // أخرى
    ),
    allowNull: false
  },
  
  // عنوان الإشعار
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 200]
    }
  },
  
  // محتوى الإشعار
  message: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 1000]
    }
  },
  
  // أولوية الإشعار
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium',
    allowNull: false
  },
  
  // حالة القراءة
  is_read: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  
  // تاريخ القراءة
  read_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  // طرق الإرسال
  delivery_methods: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {
      push: true,      // إشعار فوري في التطبيق
      email: false,    // بريد إلكتروني
      sms: false       // رسالة نصية
    }
  },
  
  // حالة الإرسال
  delivery_status: {
    type: DataTypes.JSONB,
    allowNull: false,
    defaultValue: {
      push: 'pending',    // pending, sent, failed
      email: 'pending',
      sms: 'pending'
    }
  },
  
  // تفاصيل الإرسال
  delivery_details: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  },
  
  // تاريخ انتهاء الصلاحية
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  // رابط الإجراء (اختياري)
  action_url: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  
  // نص زر الإجراء
  action_text: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  
  // معلومات إضافية
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'notifications',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['request_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['is_read']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['expires_at']
    }
  ]
});

// طرق مساعدة
Notification.prototype.markAsRead = async function() {
  this.is_read = true;
  this.read_at = new Date();
  await this.save();
};

Notification.prototype.isExpired = function() {
  return this.expires_at && this.expires_at < new Date();
};

Notification.prototype.canBeDelivered = function() {
  return !this.isExpired();
};

Notification.prototype.updateDeliveryStatus = async function(method, status, details = {}) {
  const deliveryStatus = { ...this.delivery_status };
  const deliveryDetails = { ...this.delivery_details };
  
  deliveryStatus[method] = status;
  deliveryDetails[method] = {
    ...deliveryDetails[method],
    ...details,
    updated_at: new Date()
  };
  
  this.delivery_status = deliveryStatus;
  this.delivery_details = deliveryDetails;
  await this.save();
};

// إنشاء إشعار جديد مع طرق مساعدة
Notification.createForUser = async function(userId, data) {
  return await this.create({
    user_id: userId,
    ...data
  });
};

Notification.createForRequest = async function(requestId, userId, data) {
  return await this.create({
    request_id: requestId,
    user_id: userId,
    ...data
  });
};

module.exports = Notification;
