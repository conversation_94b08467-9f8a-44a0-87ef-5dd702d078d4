const { validationResult } = require('express-validator');
const { Request, User, Image } = require('../models');
const { createError, asyncHandler } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// الحصول على قائمة الطلبات
const getRequests = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const {
    page = 1,
    limit = 20,
    status,
    request_type,
    priority,
    search,
    date_from,
    date_to
  } = req.query;

  const offset = (page - 1) * limit;
  const where = {};

  // فلترة حسب المستخدم (المواطنون يرون طلباتهم فقط)
  if (req.user.user_type === 'citizen') {
    where.citizen_id = req.user.id;
  }

  // فلترة حسب الحالة
  if (status) {
    where.status = status;
  }

  // فلترة حسب نوع الطلب
  if (request_type) {
    where.request_type = request_type;
  }

  // فلترة حسب الأولوية
  if (priority) {
    where.priority = priority;
  }

  // البحث في العنوان أو الوصف أو رقم الطلب
  if (search) {
    where[Op.or] = [
      { title: { [Op.iLike]: `%${search}%` } },
      { description: { [Op.iLike]: `%${search}%` } },
      { request_number: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // فلترة حسب التاريخ
  if (date_from || date_to) {
    where.created_at = {};
    if (date_from) {
      where.created_at[Op.gte] = new Date(date_from);
    }
    if (date_to) {
      where.created_at[Op.lte] = new Date(date_to);
    }
  }

  const { count, rows: requests } = await Request.findAndCountAll({
    where,
    include: [
      {
        model: User,
        as: 'citizen',
        attributes: ['id', 'full_name', 'phone_number', 'email']
      },
      {
        model: User,
        as: 'assignedEmployee',
        attributes: ['id', 'full_name', 'email'],
        required: false
      }
    ],
    limit: parseInt(limit),
    offset,
    order: [['created_at', 'DESC']]
  });

  res.json({
    success: true,
    data: {
      requests,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
});

// إنشاء طلب جديد
const createRequest = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const {
    request_type,
    title,
    description,
    priority = 'medium',
    additional_data = {}
  } = req.body;

  const request = await Request.create({
    citizen_id: req.user.id,
    request_type,
    title,
    description,
    priority,
    additional_data,
    status: 'pending'
  });

  // تحميل بيانات المواطن
  await request.reload({
    include: [
      {
        model: User,
        as: 'citizen',
        attributes: ['id', 'full_name', 'phone_number', 'email']
      }
    ]
  });

  res.status(201).json({
    success: true,
    message: 'تم إنشاء الطلب بنجاح',
    data: { request }
  });
});

// الحصول على طلب محدد
const getRequestById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const request = await Request.findByPk(id, {
    include: [
      {
        model: User,
        as: 'citizen',
        attributes: ['id', 'full_name', 'phone_number', 'email']
      },
      {
        model: User,
        as: 'assignedEmployee',
        attributes: ['id', 'full_name', 'email'],
        required: false
      },
      {
        model: Image,
        as: 'images',
        attributes: ['id', 'original_name', 'filename', 'attachment_type', 'verification_status']
      }
    ]
  });

  if (!request) {
    throw createError(404, 'الطلب غير موجود', 'غير موجود');
  }

  res.json({
    success: true,
    data: { request }
  });
});

// تحديث طلب
const updateRequest = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { id } = req.params;
  const { title, description, additional_data } = req.body;

  const request = await Request.findByPk(id);
  if (!request) {
    throw createError(404, 'الطلب غير موجود', 'غير موجود');
  }

  // التحقق من إمكانية التعديل
  if (!request.canBeModified()) {
    throw createError(400, 'لا يمكن تعديل هذا الطلب في حالته الحالية', 'غير قابل للتعديل');
  }

  // تحديث البيانات
  if (title) request.title = title;
  if (description) request.description = description;
  if (additional_data) request.additional_data = { ...request.additional_data, ...additional_data };

  await request.save();

  res.json({
    success: true,
    message: 'تم تحديث الطلب بنجاح',
    data: { request }
  });
});

// حذف طلب
const deleteRequest = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const request = await Request.findByPk(id);
  if (!request) {
    throw createError(404, 'الطلب غير موجود', 'غير موجود');
  }

  // التحقق من إمكانية الحذف
  if (!request.canBeModified()) {
    throw createError(400, 'لا يمكن حذف هذا الطلب في حالته الحالية', 'غير قابل للحذف');
  }

  await request.destroy();

  res.json({
    success: true,
    message: 'تم حذف الطلب بنجاح'
  });
});

// تحديث حالة الطلب
const updateRequestStatus = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { id } = req.params;
  const {
    status,
    employee_notes,
    rejection_reason,
    expected_completion_date
  } = req.body;

  const request = await Request.findByPk(id);
  if (!request) {
    throw createError(404, 'الطلب غير موجود', 'غير موجود');
  }

  // تحديث الحالة
  request.status = status;
  if (employee_notes) request.employee_notes = employee_notes;
  if (rejection_reason) request.rejection_reason = rejection_reason;
  if (expected_completion_date) request.expected_completion_date = expected_completion_date;

  // تحديث تاريخ الإنجاز إذا كانت الحالة مكتملة
  if (status === 'completed') {
    request.actual_completion_date = new Date();
  }

  await request.save();

  res.json({
    success: true,
    message: 'تم تحديث حالة الطلب بنجاح',
    data: { request }
  });
});

// إسناد طلب إلى موظف
const assignRequest = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق هذه الوظيفة لاحقاً'
  });
});

// إلغاء إسناد طلب
const unassignRequest = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق هذه الوظيفة لاحقاً'
  });
});

// تقييم الطلب
const rateRequest = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق هذه الوظيفة لاحقاً'
  });
});

// الحصول على إحصائيات الطلبات
const getRequestStats = asyncHandler(async (req, res) => {
  const totalRequests = await Request.count();
  const pendingRequests = await Request.count({ where: { status: 'pending' } });
  const completedRequests = await Request.count({ where: { status: 'completed' } });
  const rejectedRequests = await Request.count({ where: { status: 'rejected' } });

  res.json({
    success: true,
    data: {
      total_requests: totalRequests,
      pending_requests: pendingRequests,
      completed_requests: completedRequests,
      rejected_requests: rejectedRequests
    }
  });
});

// الحصول على الطلبات المسندة للموظف الحالي
const getAssignedRequests = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق هذه الوظيفة لاحقاً',
    data: { requests: [] }
  });
});

module.exports = {
  getRequests,
  createRequest,
  getRequestById,
  updateRequest,
  deleteRequest,
  updateRequestStatus,
  assignRequest,
  unassignRequest,
  rateRequest,
  getRequestStats,
  getAssignedRequests
};
