const { validationResult } = require('express-validator');
const { Report, User } = require('../models');
const { createError, asyncHandler } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// الحصول على قائمة التقارير
const getReports = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const {
    page = 1,
    limit = 20,
    report_type,
    status,
    is_public,
    date_from,
    date_to
  } = req.query;

  const offset = (page - 1) * limit;
  const where = {};

  // فلترة حسب نوع التقرير
  if (report_type) {
    where.report_type = report_type;
  }

  // فلترة حسب الحالة
  if (status) {
    where.status = status;
  }

  // فلترة حسب العمومية
  if (is_public !== undefined) {
    where.is_public = is_public === 'true';
  }

  // فلترة حسب التاريخ
  if (date_from || date_to) {
    where.created_at = {};
    if (date_from) {
      where.created_at[Op.gte] = new Date(date_from);
    }
    if (date_to) {
      where.created_at[Op.lte] = new Date(date_to);
    }
  }

  const { count, rows: reports } = await Report.findAndCountAll({
    where,
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'full_name', 'email']
      }
    ],
    limit: parseInt(limit),
    offset,
    order: [['created_at', 'DESC']]
  });

  res.json({
    success: true,
    data: {
      reports,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
});

// إنشاء تقرير جديد
const createReport = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const {
    report_type,
    title,
    description,
    period_start,
    period_end,
    is_public = false,
    metadata = {}
  } = req.body;

  const report = await Report.create({
    report_type,
    title,
    description,
    period_start,
    period_end,
    created_by: req.user.id,
    is_public,
    metadata,
    status: 'generating'
  });

  // بدء عملية إنشاء التقرير (في الخلفية)
  // هنا يمكن إضافة منطق إنشاء التقرير الفعلي

  res.status(201).json({
    success: true,
    message: 'تم بدء إنشاء التقرير بنجاح',
    data: { report }
  });
});

// إنشاء تقرير شهري تلقائي
const createMonthlyReport = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق التقارير الشهرية التلقائية لاحقاً'
  });
});

// إنشاء تقرير سنوي تلقائي
const createYearlyReport = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق التقارير السنوية التلقائية لاحقاً'
  });
});

// الحصول على تقرير محدد
const getReportById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const report = await Report.findByPk(id, {
    include: [
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'full_name', 'email']
      }
    ]
  });

  if (!report) {
    throw createError(404, 'التقرير غير موجود', 'غير موجود');
  }

  res.json({
    success: true,
    data: { report }
  });
});

// تحديث تقرير
const updateReport = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { id } = req.params;
  const { title, description, is_public, metadata } = req.body;

  const report = await Report.findByPk(id);
  if (!report) {
    throw createError(404, 'التقرير غير موجود', 'غير موجود');
  }

  // تحديث البيانات
  if (title) report.title = title;
  if (description !== undefined) report.description = description;
  if (is_public !== undefined) report.is_public = is_public;
  if (metadata) report.metadata = { ...report.metadata, ...metadata };

  await report.save();

  res.json({
    success: true,
    message: 'تم تحديث التقرير بنجاح',
    data: { report }
  });
});

// حذف تقرير
const deleteReport = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const report = await Report.findByPk(id);
  if (!report) {
    throw createError(404, 'التقرير غير موجود', 'غير موجود');
  }

  await report.destroy();

  res.json({
    success: true,
    message: 'تم حذف التقرير بنجاح'
  });
});

// تحميل تقرير
const downloadReport = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق تحميل التقارير لاحقاً'
  });
});

// عرض تقرير في المتصفح
const viewReport = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق عرض التقارير لاحقاً'
  });
});

// إعادة إنشاء تقرير
const regenerateReport = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const report = await Report.findByPk(id);
  if (!report) {
    throw createError(404, 'التقرير غير موجود', 'غير موجود');
  }

  // إعادة تعيين حالة التقرير
  report.status = 'generating';
  report.data = {};
  report.statistics = {};
  await report.save();

  res.json({
    success: true,
    message: 'تم بدء إعادة إنشاء التقرير بنجاح',
    data: { report }
  });
});

// الحصول على إحصائيات التقارير
const getReportStats = asyncHandler(async (req, res) => {
  const totalReports = await Report.count();
  const completedReports = await Report.count({ where: { status: 'completed' } });
  const generatingReports = await Report.count({ where: { status: 'generating' } });
  const failedReports = await Report.count({ where: { status: 'failed' } });

  res.json({
    success: true,
    data: {
      total_reports: totalReports,
      completed_reports: completedReports,
      generating_reports: generatingReports,
      failed_reports: failedReports
    }
  });
});

// الحصول على التقارير العامة
const getPublicReports = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    report_type
  } = req.query;

  const offset = (page - 1) * limit;
  const where = { is_public: true, status: 'completed' };

  if (report_type) {
    where.report_type = report_type;
  }

  const { count, rows: reports } = await Report.findAndCountAll({
    where,
    attributes: ['id', 'title', 'description', 'report_type', 'period_start', 'period_end', 'created_at'],
    limit: parseInt(limit),
    offset,
    order: [['created_at', 'DESC']]
  });

  res.json({
    success: true,
    data: {
      reports,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
});

// الحصول على إحصائيات سريعة للوحة التحكم
const getDashboardStats = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق إحصائيات لوحة التحكم لاحقاً',
    data: {}
  });
});

// تصدير بيانات التقرير
const exportReportData = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق تصدير البيانات لاحقاً'
  });
});

// جدولة تقرير دوري
const scheduleReport = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق جدولة التقارير لاحقاً'
  });
});

module.exports = {
  getReports,
  createReport,
  createMonthlyReport,
  createYearlyReport,
  getReportById,
  updateReport,
  deleteReport,
  downloadReport,
  viewReport,
  regenerateReport,
  getReportStats,
  getPublicReports,
  getDashboardStats,
  exportReportData,
  scheduleReport
};
