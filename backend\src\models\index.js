const { sequelize } = require('../config/database');

// دالة لاختبار الاتصال بقاعدة البيانات
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    throw error;
  }
};

// استيراد النماذج
const User = require('./User');
const Request = require('./Request');
const Image = require('./Image');
const Notification = require('./Notification');
const Report = require('./Report');

// تعريف العلاقات بين النماذج

// علاقة المستخدم مع الطلبات
User.hasMany(Request, {
  foreignKey: 'citizen_id',
  as: 'requests'
});

Request.belongsTo(User, {
  foreignKey: 'citizen_id',
  as: 'citizen'
});

// علاقة الموظف مع الطلبات المسندة إليه
User.hasMany(Request, {
  foreignKey: 'assigned_to',
  as: 'assigned_requests'
});

Request.belongsTo(User, {
  foreignKey: 'assigned_to',
  as: 'assignedEmployee'
});

// علاقة الطلب مع الصور/المرفقات
Request.hasMany(Image, {
  foreignKey: 'request_id',
  as: 'images',
  onDelete: 'CASCADE'
});

Image.belongsTo(Request, {
  foreignKey: 'request_id',
  as: 'request'
});

// علاقة الموظف مع التحقق من الصور
User.hasMany(Image, {
  foreignKey: 'verified_by',
  as: 'verified_images'
});

Image.belongsTo(User, {
  foreignKey: 'verified_by',
  as: 'verifier'
});

// علاقة المستخدم مع الإشعارات
User.hasMany(Notification, {
  foreignKey: 'user_id',
  as: 'notifications',
  onDelete: 'CASCADE'
});

Notification.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// علاقة الطلب مع الإشعارات
Request.hasMany(Notification, {
  foreignKey: 'request_id',
  as: 'notifications',
  onDelete: 'CASCADE'
});

Notification.belongsTo(Request, {
  foreignKey: 'request_id',
  as: 'request'
});

// علاقة المستخدم مع التقارير
User.hasMany(Report, {
  foreignKey: 'created_by',
  as: 'reports'
});

Report.belongsTo(User, {
  foreignKey: 'created_by',
  as: 'creator'
});

// دالة لمزامنة قاعدة البيانات
const syncDatabase = async (force = false) => {
  try {
    console.log('🔄 بدء مزامنة قاعدة البيانات...');
    
    // مزامنة النماذج بالترتيب الصحيح
    await User.sync({ force });
    console.log('✅ تم مزامنة جدول المستخدمين');
    
    await Request.sync({ force });
    console.log('✅ تم مزامنة جدول الطلبات');
    
    await Image.sync({ force });
    console.log('✅ تم مزامنة جدول الصور');
    
    await Notification.sync({ force });
    console.log('✅ تم مزامنة جدول الإشعارات');
    
    await Report.sync({ force });
    console.log('✅ تم مزامنة جدول التقارير');
    
    console.log('🎉 تم إنشاء قاعدة البيانات بنجاح!');
  } catch (error) {
    console.error('❌ خطأ في مزامنة قاعدة البيانات:', error);
    throw error;
  }
};

// دالة لإنشاء البيانات الأولية
const seedDatabase = async () => {
  try {
    console.log('🌱 بدء إنشاء البيانات الأولية...');
    
    // إنشاء مستخدم إداري افتراضي
    const adminExists = await User.findOne({
      where: { user_type: 'admin' }
    });
    
    if (!adminExists) {
      await User.create({
        full_name: 'مدير النظام',
        phone_number: '+966500000000',
        email: '<EMAIL>',
        user_type: 'admin',
        password: 'Admin@123456',
        is_active: true,
        is_verified: true
      });
      console.log('✅ تم إنشاء المستخدم الإداري');
    }
    
    // إنشاء موظف تجريبي
    const employeeExists = await User.findOne({
      where: { user_type: 'employee' }
    });
    
    if (!employeeExists) {
      await User.create({
        full_name: 'موظف الخدمات',
        phone_number: '+966500000001',
        email: '<EMAIL>',
        user_type: 'employee',
        password: 'Employee@123456',
        is_active: true,
        is_verified: true
      });
      console.log('✅ تم إنشاء الموظف التجريبي');
    }
    
    console.log('🎉 تم إنشاء البيانات الأولية بنجاح!');
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات الأولية:', error);
    throw error;
  }
};

module.exports = {
  sequelize,
  User,
  Request,
  Image,
  Notification,
  Report,
  testConnection,
  syncDatabase,
  seedDatabase
};
