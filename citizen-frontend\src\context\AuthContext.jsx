import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI, handleAPIError } from '../services/api';

// إنشاء Context
const AuthContext = createContext();

// الحالات الأولية
const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// أنواع الإجراءات
const actionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_ERROR: 'SET_ERROR',
  LOGOUT: 'LOGOUT',
  CLEAR_ERROR: 'CLEAR_ERROR',
};

// Reducer للتعامل مع الحالات
const authReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };

    case actionTypes.SET_USER:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

    case actionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };

    case actionTypes.LOGOUT:
      return {
        ...initialState,
        isLoading: false,
      };

    case actionTypes.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
};

// مزود Context
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // التحقق من وجود token في localStorage عند تحميل التطبيق
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');

      if (token && user) {
        try {
          // التحقق من صحة الـ token
          const response = await authAPI.getCurrentUser();
          dispatch({
            type: actionTypes.SET_USER,
            payload: {
              user: response.data.data.user,
              token,
            },
          });
        } catch (error) {
          // إذا كان الـ token غير صحيح، قم بإزالته
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          dispatch({ type: actionTypes.LOGOUT });
        }
      } else {
        dispatch({ type: actionTypes.SET_LOADING, payload: false });
      }
    };

    checkAuth();
  }, []);

  // تسجيل مستخدم جديد
  const register = async (userData) => {
    dispatch({ type: actionTypes.SET_LOADING, payload: true });
    try {
      const response = await authAPI.register(userData);
      dispatch({ type: actionTypes.SET_LOADING, payload: false });
      return { success: true, data: response.data };
    } catch (error) {
      const errorInfo = handleAPIError(error);
      dispatch({ type: actionTypes.SET_ERROR, payload: errorInfo.message });
      return { success: false, error: errorInfo.message };
    }
  };

  // إرسال رمز OTP
  const sendOTP = async (phoneNumber) => {
    dispatch({ type: actionTypes.SET_LOADING, payload: true });
    try {
      const response = await authAPI.sendOTP(phoneNumber);
      dispatch({ type: actionTypes.SET_LOADING, payload: false });
      return { success: true, data: response.data };
    } catch (error) {
      const errorInfo = handleAPIError(error);
      dispatch({ type: actionTypes.SET_ERROR, payload: errorInfo.message });
      return { success: false, error: errorInfo.message };
    }
  };

  // التحقق من رمز OTP وتسجيل الدخول
  const verifyOTP = async (phoneNumber, otpCode) => {
    dispatch({ type: actionTypes.SET_LOADING, payload: true });
    try {
      const response = await authAPI.verifyOTP(phoneNumber, otpCode);
      const { token, user } = response.data.data;

      // حفظ البيانات في localStorage
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));

      dispatch({
        type: actionTypes.SET_USER,
        payload: { user, token },
      });

      return { success: true, data: response.data };
    } catch (error) {
      const errorInfo = handleAPIError(error);
      dispatch({ type: actionTypes.SET_ERROR, payload: errorInfo.message });
      return { success: false, error: errorInfo.message };
    }
  };

  // تسجيل دخول الموظفين
  const login = async (email, password) => {
    dispatch({ type: actionTypes.SET_LOADING, payload: true });
    try {
      const response = await authAPI.login(email, password);
      const { token, user } = response.data.data;

      // حفظ البيانات في localStorage
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));

      dispatch({
        type: actionTypes.SET_USER,
        payload: { user, token },
      });

      return { success: true, data: response.data };
    } catch (error) {
      const errorInfo = handleAPIError(error);
      dispatch({ type: actionTypes.SET_ERROR, payload: errorInfo.message });
      return { success: false, error: errorInfo.message };
    }
  };

  // تسجيل الخروج
  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
    } finally {
      // إزالة البيانات من localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      dispatch({ type: actionTypes.LOGOUT });
    }
  };

  // تحديث الملف الشخصي
  const updateProfile = async (profileData) => {
    dispatch({ type: actionTypes.SET_LOADING, payload: true });
    try {
      const response = await authAPI.updateProfile(profileData);
      const updatedUser = response.data.data.user;

      // تحديث البيانات في localStorage
      localStorage.setItem('user', JSON.stringify(updatedUser));

      dispatch({
        type: actionTypes.SET_USER,
        payload: { user: updatedUser, token: state.token },
      });

      return { success: true, data: response.data };
    } catch (error) {
      const errorInfo = handleAPIError(error);
      dispatch({ type: actionTypes.SET_ERROR, payload: errorInfo.message });
      return { success: false, error: errorInfo.message };
    }
  };

  // مسح الأخطاء
  const clearError = () => {
    dispatch({ type: actionTypes.CLEAR_ERROR });
  };

  // القيم التي سيتم توفيرها للمكونات
  const value = {
    ...state,
    register,
    sendOTP,
    verifyOTP,
    login,
    logout,
    updateProfile,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook لاستخدام AuthContext
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
