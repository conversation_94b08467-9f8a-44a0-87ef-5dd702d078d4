const { ValidationError, UniqueConstraintError, ForeignKeyConstraintError } = require('sequelize');

// معالج الأخطاء العام
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // تسجيل الخطأ
  console.error('❌ خطأ في الخادم:', {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // خطأ التحقق من صحة البيانات (Sequelize)
  if (err instanceof ValidationError) {
    const message = err.errors.map(e => e.message).join(', ');
    return res.status(400).json({
      success: false,
      error: 'خطأ في التحقق من صحة البيانات',
      message,
      details: err.errors
    });
  }

  // خطأ القيد الفريد (Unique Constraint)
  if (err instanceof UniqueConstraintError) {
    const field = err.errors[0]?.path || 'unknown';
    const fieldNames = {
      'phone_number': 'رقم الهاتف',
      'email': 'البريد الإلكتروني',
      'national_id': 'رقم الهوية',
      'request_number': 'رقم الطلب'
    };
    
    return res.status(400).json({
      success: false,
      error: 'البيانات مكررة',
      message: `${fieldNames[field] || field} موجود مسبقاً`,
      field
    });
  }

  // خطأ المفتاح الخارجي (Foreign Key)
  if (err instanceof ForeignKeyConstraintError) {
    return res.status(400).json({
      success: false,
      error: 'خطأ في الربط',
      message: 'البيانات المرجعية غير صحيحة'
    });
  }

  // خطأ JWT
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      error: 'رمز المصادقة غير صحيح',
      message: 'يرجى تسجيل الدخول مرة أخرى'
    });
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      error: 'انتهت صلاحية رمز المصادقة',
      message: 'يرجى تسجيل الدخول مرة أخرى'
    });
  }

  // خطأ Multer (رفع الملفات)
  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.status(400).json({
      success: false,
      error: 'حجم الملف كبير جداً',
      message: 'الحد الأقصى لحجم الملف هو 5 ميجابايت'
    });
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.status(400).json({
      success: false,
      error: 'نوع الملف غير مدعوم',
      message: 'يرجى رفع ملفات من النوع المسموح فقط'
    });
  }

  // أخطاء HTTP المخصصة
  if (err.statusCode) {
    return res.status(err.statusCode).json({
      success: false,
      error: err.error || 'خطأ في الخادم',
      message: err.message
    });
  }

  // خطأ عام في الخادم
  res.status(500).json({
    success: false,
    error: 'خطأ داخلي في الخادم',
    message: process.env.NODE_ENV === 'production' 
      ? 'حدث خطأ غير متوقع، يرجى المحاولة لاحقاً' 
      : err.message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

// دالة لإنشاء خطأ مخصص
const createError = (statusCode, message, error = null) => {
  const err = new Error(message);
  err.statusCode = statusCode;
  err.error = error;
  return err;
};

// دالة للتعامل مع الأخطاء غير المتزامنة
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

module.exports = {
  errorHandler,
  createError,
  asyncHandler
};
