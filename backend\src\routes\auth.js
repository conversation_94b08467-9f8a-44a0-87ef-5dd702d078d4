const express = require('express');
const { body } = require('express-validator');
const authController = require('../controllers/authController');
const { loginLimiter, otpLimiter } = require('../middleware/rateLimiter');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// تسجيل مستخدم جديد (مواطن)
router.post('/register', [
  body('full_name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم الكامل يجب أن يكون بين 2 و 100 حرف'),
  body('phone_number')
    .isMobilePhone('ar-SA')
    .withMessage('رقم الهاتف غير صحيح'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  body('national_id')
    .optional()
    .isLength({ min: 10, max: 10 })
    .withMessage('رقم الهوية يجب أن يكون 10 أرقام')
], authController.register);

// إرسال رمز التحقق OTP
router.post('/send-otp', otpLimiter, [
  body('phone_number')
    .isMobilePhone('ar-SA')
    .withMessage('رقم الهاتف غير صحيح')
], authController.sendOTP);

// التحقق من رمز OTP وتسجيل الدخول
router.post('/verify-otp', loginLimiter, [
  body('phone_number')
    .isMobilePhone('ar-SA')
    .withMessage('رقم الهاتف غير صحيح'),
  body('otp_code')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('رمز التحقق يجب أن يكون 6 أرقام')
], authController.verifyOTP);

// تسجيل دخول الموظفين والإدارة
router.post('/login', loginLimiter, [
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
], authController.login);

// تسجيل الخروج
router.post('/logout', authenticate, authController.logout);

// تحديث الرمز المميز
router.post('/refresh-token', authenticate, authController.refreshToken);

// التحقق من صحة الرمز المميز
router.get('/verify-token', authenticate, authController.verifyToken);

// الحصول على المستخدم الحالي
router.get('/me', authenticate, authController.getCurrentUser);

// تحديث الملف الشخصي
router.put('/profile', authenticate, [
  body('full_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم الكامل يجب أن يكون بين 2 و 100 حرف'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  body('address')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('العنوان يجب أن يكون أقل من 500 حرف')
], authController.updateProfile);

// تغيير كلمة المرور (للموظفين والإدارة)
router.put('/change-password', authenticate, [
  body('current_password')
    .notEmpty()
    .withMessage('كلمة المرور الحالية مطلوبة'),
  body('new_password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('كلمة المرور الجديدة يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص'),
  body('confirm_password')
    .custom((value, { req }) => {
      if (value !== req.body.new_password) {
        throw new Error('تأكيد كلمة المرور غير متطابق');
      }
      return true;
    })
], authController.changePassword);

// طلب إعادة تعيين كلمة المرور
router.post('/forgot-password', [
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح')
], authController.forgotPassword);

// إعادة تعيين كلمة المرور
router.post('/reset-password', [
  body('token')
    .notEmpty()
    .withMessage('رمز إعادة التعيين مطلوب'),
  body('new_password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص')
], authController.resetPassword);

module.exports = router;
