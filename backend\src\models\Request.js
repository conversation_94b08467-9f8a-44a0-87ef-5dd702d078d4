const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Request = sequelize.define('Request', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  
  // رقم الطلب (تسلسلي)
  request_number: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true
  },
  
  // معرف المواطن
  citizen_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  // نوع الطلب
  request_type: {
    type: DataTypes.ENUM(
      'birth_certificate',      // شهادة ميلاد
      'death_certificate',      // شهادة وفاة
      'marriage_certificate',   // شهادة زواج
      'divorce_certificate',    // شهادة طلاق
      'residence_certificate',  // شهادة إقامة
      'income_certificate',     // شهادة دخل
      'employment_certificate', // شهادة عمل
      'student_certificate',    // شهادة طالب
      'health_certificate',     // شهادة صحية
      'police_clearance',       // شهادة حسن سيرة وسلوك
      'commercial_license',     // رخصة تجارية
      'building_permit',        // رخصة بناء
      'other'                   // أخرى
    ),
    allowNull: false
  },
  
  // عنوان الطلب
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [5, 200]
    }
  },
  
  // وصف الطلب
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [10, 2000]
    }
  },
  
  // حالة الطلب
  status: {
    type: DataTypes.ENUM(
      'pending',        // قيد الانتظار
      'under_review',   // قيد المراجعة
      'in_progress',    // قيد التنفيذ
      'completed',      // مكتمل
      'rejected',       // مرفوض
      'cancelled'       // ملغي
    ),
    defaultValue: 'pending',
    allowNull: false
  },
  
  // أولوية الطلب
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium',
    allowNull: false
  },
  
  // الموظف المسؤول
  assigned_to: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  // ملاحظات الموظف
  employee_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  // سبب الرفض (إن وجد)
  rejection_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  // تاريخ الإنجاز المتوقع
  expected_completion_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  // تاريخ الإنجاز الفعلي
  actual_completion_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  // معلومات إضافية (JSON)
  additional_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  },
  
  // تقييم المواطن للخدمة
  citizen_rating: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 5
    }
  },
  
  citizen_feedback: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'requests',
  indexes: [
    {
      unique: true,
      fields: ['request_number']
    },
    {
      fields: ['citizen_id']
    },
    {
      fields: ['request_type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['assigned_to']
    },
    {
      fields: ['created_at']
    }
  ]
});

// إنشاء رقم الطلب تلقائياً
Request.beforeCreate(async (request) => {
  if (!request.request_number) {
    const year = new Date().getFullYear();
    const count = await Request.count({
      where: sequelize.where(
        sequelize.fn('EXTRACT', sequelize.literal('YEAR FROM created_at')),
        year
      )
    });
    request.request_number = `REQ-${year}-${String(count + 1).padStart(6, '0')}`;
  }
});

// طرق مساعدة
Request.prototype.canBeModified = function() {
  return ['pending', 'under_review'].includes(this.status);
};

Request.prototype.isCompleted = function() {
  return this.status === 'completed';
};

Request.prototype.isActive = function() {
  return !['completed', 'rejected', 'cancelled'].includes(this.status);
};

module.exports = Request;
