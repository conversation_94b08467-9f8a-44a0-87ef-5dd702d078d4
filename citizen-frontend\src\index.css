@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

:root {
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  direction: rtl;
  text-align: right;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #f8fafc;
  color: #1e293b;
  font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
  direction: rtl;
}

/* تخصيص الأزرار */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-success {
  @apply bg-success-600 hover:bg-success-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-danger {
  @apply bg-danger-600 hover:bg-danger-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

/* تخصيص النماذج */
.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* تخصيص البطاقات */
.card {
  @apply bg-white rounded-lg shadow-md border border-gray-200;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* تخصيص الحالات */
.status-pending {
  @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-under-review {
  @apply bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-in-progress {
  @apply bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-completed {
  @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-rejected {
  @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-cancelled {
  @apply bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium;
}

/* تخصيص التنبيهات */
.alert {
  @apply p-4 rounded-lg mb-4;
}

.alert-success {
  @apply bg-success-50 border border-success-200 text-success-800;
}

.alert-error {
  @apply bg-danger-50 border border-danger-200 text-danger-800;
}

.alert-warning {
  @apply bg-warning-50 border border-warning-200 text-warning-800;
}

.alert-info {
  @apply bg-primary-50 border border-primary-200 text-primary-800;
}

/* تحسينات للغة العربية */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}
