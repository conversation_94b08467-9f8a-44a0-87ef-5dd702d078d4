import axios from 'axios';

// إعداد الـ base URL للـ API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// إنشاء instance من axios
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// إضافة interceptor للطلبات لإضافة الـ token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// إضافة interceptor للاستجابات للتعامل مع الأخطاء
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // إزالة الـ token وإعادة توجيه للصفحة الرئيسية
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// خدمات المصادقة
export const authAPI = {
  // تسجيل مستخدم جديد
  register: (userData) => api.post('/auth/register', userData),
  
  // إرسال رمز OTP
  sendOTP: (phoneNumber) => api.post('/auth/send-otp', { phone_number: phoneNumber }),
  
  // التحقق من رمز OTP وتسجيل الدخول
  verifyOTP: (phoneNumber, otpCode) => 
    api.post('/auth/verify-otp', { phone_number: phoneNumber, otp_code: otpCode }),
  
  // تسجيل دخول الموظفين
  login: (email, password) => api.post('/auth/login', { email, password }),
  
  // تسجيل الخروج
  logout: () => api.post('/auth/logout'),
  
  // الحصول على المستخدم الحالي
  getCurrentUser: () => api.get('/auth/me'),
  
  // تحديث الملف الشخصي
  updateProfile: (profileData) => api.put('/auth/profile', profileData),
  
  // تغيير كلمة المرور
  changePassword: (passwordData) => api.put('/auth/change-password', passwordData),
};

// خدمات الطلبات
export const requestsAPI = {
  // الحصول على قائمة الطلبات
  getRequests: (params = {}) => api.get('/requests', { params }),
  
  // إنشاء طلب جديد
  createRequest: (requestData) => api.post('/requests', requestData),
  
  // الحصول على طلب محدد
  getRequest: (id) => api.get(`/requests/${id}`),
  
  // تحديث طلب
  updateRequest: (id, requestData) => api.put(`/requests/${id}`, requestData),
  
  // حذف طلب
  deleteRequest: (id) => api.delete(`/requests/${id}`),
  
  // تقييم طلب
  rateRequest: (id, rating, feedback) => 
    api.post(`/requests/${id}/rating`, { citizen_rating: rating, citizen_feedback: feedback }),
  
  // الحصول على إحصائيات الطلبات
  getRequestStats: (params = {}) => api.get('/requests/stats/overview', { params }),
};

// خدمات الصور والمرفقات
export const imagesAPI = {
  // رفع صور لطلب
  uploadImages: (requestId, formData) => 
    api.post(`/images/upload/${requestId}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
  
  // الحصول على صور طلب
  getRequestImages: (requestId, params = {}) => 
    api.get(`/images/request/${requestId}`, { params }),
  
  // الحصول على صورة محددة
  getImage: (id) => api.get(`/images/${id}`),
  
  // تحديث معلومات صورة
  updateImage: (id, imageData) => api.put(`/images/${id}`, imageData),
  
  // حذف صورة
  deleteImage: (id) => api.delete(`/images/${id}`),
  
  // تحميل صورة
  downloadImage: (id) => api.get(`/images/${id}/download`, { responseType: 'blob' }),
  
  // عرض صورة
  getImageUrl: (id) => `${API_BASE_URL}/images/${id}/view`,
};

// خدمات الإشعارات
export const notificationsAPI = {
  // الحصول على إشعارات المستخدم
  getNotifications: (params = {}) => api.get('/notifications', { params }),
  
  // الحصول على إشعار محدد
  getNotification: (id) => api.get(`/notifications/${id}`),
  
  // تحديد إشعار كمقروء
  markAsRead: (id) => api.patch(`/notifications/${id}/read`),
  
  // تحديد جميع الإشعارات كمقروءة
  markAllAsRead: () => api.patch('/notifications/mark-all/read'),
  
  // حذف إشعار
  deleteNotification: (id) => api.delete(`/notifications/${id}`),
  
  // حذف جميع الإشعارات المقروءة
  clearReadNotifications: () => api.delete('/notifications/read/clear'),
  
  // الحصول على عدد الإشعارات غير المقروءة
  getUnreadCount: () => api.get('/notifications/count/unread'),
};

// خدمات المستخدمين
export const usersAPI = {
  // الحصول على قائمة المستخدمين
  getUsers: (params = {}) => api.get('/users', { params }),
  
  // الحصول على مستخدم محدد
  getUser: (id) => api.get(`/users/${id}`),
  
  // تحديث مستخدم
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  
  // رفع صورة الملف الشخصي
  uploadProfileImage: (id, formData) => 
    api.post(`/users/${id}/profile-image`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
  
  // حذف صورة الملف الشخصي
  deleteProfileImage: (id) => api.delete(`/users/${id}/profile-image`),
  
  // الحصول على طلبات المستخدم
  getUserRequests: (id, params = {}) => api.get(`/users/${id}/requests`, { params }),
  
  // الحصول على إشعارات المستخدم
  getUserNotifications: (id, params = {}) => api.get(`/users/${id}/notifications`, { params }),
};

// خدمات التقارير
export const reportsAPI = {
  // الحصول على قائمة التقارير
  getReports: (params = {}) => api.get('/reports', { params }),
  
  // إنشاء تقرير جديد
  createReport: (reportData) => api.post('/reports', reportData),
  
  // الحصول على تقرير محدد
  getReport: (id) => api.get(`/reports/${id}`),
  
  // تحميل تقرير
  downloadReport: (id) => api.get(`/reports/${id}/download`, { responseType: 'blob' }),
  
  // الحصول على التقارير العامة
  getPublicReports: (params = {}) => api.get('/reports/public/list', { params }),
  
  // الحصول على إحصائيات سريعة
  getDashboardStats: () => api.get('/reports/dashboard/quick-stats'),
};

// دالة مساعدة للتعامل مع أخطاء API
export const handleAPIError = (error) => {
  if (error.response) {
    // الخادم أرجع استجابة مع كود خطأ
    return {
      message: error.response.data?.message || 'حدث خطأ في الخادم',
      status: error.response.status,
      data: error.response.data
    };
  } else if (error.request) {
    // الطلب تم إرساله لكن لم يتم استلام استجابة
    return {
      message: 'لا يمكن الاتصال بالخادم، يرجى التحقق من الاتصال بالإنترنت',
      status: 0
    };
  } else {
    // خطأ في إعداد الطلب
    return {
      message: error.message || 'حدث خطأ غير متوقع',
      status: -1
    };
  }
};

export default api;
