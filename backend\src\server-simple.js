const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// إعداد الوسطاء الأساسية
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(cors());

app.use(compression());
app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// مسار الصحة
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// مسار الجذر
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في منصة المواطن - Citizen Platform API',
    version: '1.0.0',
    documentation: '/api/docs',
    health: '/health'
  });
});

// مسار اختبار API
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API يعمل بشكل صحيح',
    timestamp: new Date().toISOString()
  });
});

// معالج الأخطاء 404
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود',
    path: req.originalUrl
  });
});

// معالج الأخطاء العام
app.use((err, req, res, next) => {
  console.error('خطأ في الخادم:', err);
  res.status(500).json({
    success: false,
    error: 'خطأ داخلي في الخادم',
    message: process.env.NODE_ENV === 'production' 
      ? 'حدث خطأ غير متوقع، يرجى المحاولة لاحقاً' 
      : err.message
  });
});

// بدء الخادم
app.listen(PORT, () => {
  console.log(`🎉 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الرابط: http://localhost:${PORT}`);
  console.log(`💚 الصحة: http://localhost:${PORT}/health`);
  console.log(`🧪 اختبار API: http://localhost:${PORT}/api/test`);
});

module.exports = app;
