-- إنشاء قاعدة البيانات لمنصة المواطن
-- Citizen Platform Database Setup

-- إنشاء قاعدة البيانات (يجب تشغيل هذا الأمر من خارج قاعدة البيانات)
-- CREATE DATABASE citizen_platform;

-- الاتصال بقاعدة البيانات
-- \c citizen_platform;

-- إنشاء امتدادات مفيدة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- إنشاء فهارس للبحث النصي
CREATE INDEX IF NOT EXISTS idx_users_full_name_gin ON users USING gin(full_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_requests_title_gin ON requests USING gin(title gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_requests_description_gin ON requests USING gin(description gin_trgm_ops);

-- دالة لإنشاء رقم الطلب التلقائي
CREATE OR REPLACE FUNCTION generate_request_number()
RETURNS TRIGGER AS $$
DECLARE
    year_part TEXT;
    count_part INTEGER;
    new_number TEXT;
BEGIN
    -- الحصول على السنة الحالية
    year_part := EXTRACT(YEAR FROM NOW())::TEXT;
    
    -- عد الطلبات في السنة الحالية
    SELECT COUNT(*) + 1 INTO count_part
    FROM requests 
    WHERE EXTRACT(YEAR FROM created_at) = EXTRACT(YEAR FROM NOW());
    
    -- إنشاء رقم الطلب
    new_number := 'REQ-' || year_part || '-' || LPAD(count_part::TEXT, 6, '0');
    
    -- تعيين رقم الطلب
    NEW.request_number := new_number;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء محفز لرقم الطلب التلقائي
DROP TRIGGER IF EXISTS trigger_generate_request_number ON requests;
CREATE TRIGGER trigger_generate_request_number
    BEFORE INSERT ON requests
    FOR EACH ROW
    EXECUTE FUNCTION generate_request_number();

-- دالة لتحديث تاريخ التعديل
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء محفزات لتحديث تاريخ التعديل
CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_requests_updated_at
    BEFORE UPDATE ON requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_images_updated_at
    BEFORE UPDATE ON images
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_notifications_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_reports_updated_at
    BEFORE UPDATE ON reports
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- دالة لإنشاء إشعار تلقائي عند تغيير حالة الطلب
CREATE OR REPLACE FUNCTION create_status_notification()
RETURNS TRIGGER AS $$
DECLARE
    notification_title TEXT;
    notification_message TEXT;
    notification_type TEXT;
BEGIN
    -- تحديد نوع الإشعار والرسالة حسب الحالة الجديدة
    CASE NEW.status
        WHEN 'under_review' THEN
            notification_type := 'request_under_review';
            notification_title := 'طلبك قيد المراجعة';
            notification_message := 'تم استلام طلبك رقم ' || NEW.request_number || ' وهو الآن قيد المراجعة من قبل الفريق المختص.';
        WHEN 'in_progress' THEN
            notification_type := 'request_in_progress';
            notification_title := 'بدء معالجة طلبك';
            notification_message := 'تم البدء في معالجة طلبك رقم ' || NEW.request_number || '. سيتم إشعارك عند اكتمال المعالجة.';
        WHEN 'completed' THEN
            notification_type := 'request_completed';
            notification_title := 'تم إنجاز طلبك';
            notification_message := 'تم إنجاز طلبك رقم ' || NEW.request_number || ' بنجاح. يمكنك الآن مراجعة النتائج.';
        WHEN 'rejected' THEN
            notification_type := 'request_rejected';
            notification_title := 'تم رفض طلبك';
            notification_message := 'تم رفض طلبك رقم ' || NEW.request_number || '. ' || COALESCE(NEW.rejection_reason, 'يرجى مراجعة تفاصيل الطلب.');
        WHEN 'cancelled' THEN
            notification_type := 'request_cancelled';
            notification_title := 'تم إلغاء طلبك';
            notification_message := 'تم إلغاء طلبك رقم ' || NEW.request_number || '.';
        ELSE
            RETURN NEW;
    END CASE;
    
    -- إنشاء الإشعار فقط إذا تغيرت الحالة
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO notifications (
            id,
            user_id,
            request_id,
            type,
            title,
            message,
            priority,
            delivery_methods,
            delivery_status,
            created_at,
            updated_at
        ) VALUES (
            uuid_generate_v4(),
            NEW.citizen_id,
            NEW.id,
            notification_type,
            notification_title,
            notification_message,
            'medium',
            '{"push": true, "email": true, "sms": false}',
            '{"push": "pending", "email": "pending", "sms": "pending"}',
            NOW(),
            NOW()
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء محفز للإشعارات التلقائية
DROP TRIGGER IF EXISTS trigger_create_status_notification ON requests;
CREATE TRIGGER trigger_create_status_notification
    AFTER UPDATE ON requests
    FOR EACH ROW
    EXECUTE FUNCTION create_status_notification();

-- دالة لتنظيف الإشعارات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM notifications 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مهمة دورية لتنظيف الإشعارات (يتطلب pg_cron extension)
-- SELECT cron.schedule('cleanup-notifications', '0 2 * * *', 'SELECT cleanup_expired_notifications();');

-- عرض لإحصائيات الطلبات
CREATE OR REPLACE VIEW requests_statistics AS
SELECT 
    COUNT(*) as total_requests,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
    COUNT(CASE WHEN status = 'under_review' THEN 1 END) as under_review_requests,
    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_requests,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_requests,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_requests,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_requests,
    AVG(CASE 
        WHEN status = 'completed' AND actual_completion_date IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (actual_completion_date - created_at))/86400 
    END) as avg_processing_days
FROM requests;

-- عرض للطلبات مع معلومات المواطن
CREATE OR REPLACE VIEW requests_with_citizen AS
SELECT 
    r.*,
    u.full_name as citizen_name,
    u.phone_number as citizen_phone,
    u.email as citizen_email,
    emp.full_name as employee_name
FROM requests r
JOIN users u ON r.citizen_id = u.id
LEFT JOIN users emp ON r.assigned_to = emp.id;

-- منح الصلاحيات (يجب تعديلها حسب المستخدمين الفعليين)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO citizen_platform_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO citizen_platform_user;
