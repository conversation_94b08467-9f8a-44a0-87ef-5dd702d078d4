const { validationResult } = require('express-validator');
const { User, Request, Notification } = require('../models');
const { createError, asyncHandler } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// الحصول على قائمة المستخدمين
const getUsers = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const {
    page = 1,
    limit = 20,
    search,
    user_type,
    is_active
  } = req.query;

  const offset = (page - 1) * limit;
  const where = {};

  // فلترة حسب نوع المستخدم
  if (user_type) {
    where.user_type = user_type;
  }

  // فلترة حسب حالة النشاط
  if (is_active !== undefined) {
    where.is_active = is_active === 'true';
  }

  // البحث في الاسم أو البريد الإلكتروني أو رقم الهاتف
  if (search) {
    where[Op.or] = [
      { full_name: { [Op.iLike]: `%${search}%` } },
      { email: { [Op.iLike]: `%${search}%` } },
      { phone_number: { [Op.iLike]: `%${search}%` } }
    ];
  }

  const { count, rows: users } = await User.findAndCountAll({
    where,
    attributes: { exclude: ['password', 'otp_code', 'otp_expires_at'] },
    limit: parseInt(limit),
    offset,
    order: [['created_at', 'DESC']]
  });

  res.json({
    success: true,
    data: {
      users,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
});

// الحصول على مستخدم محدد
const getUserById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await User.findByPk(id, {
    attributes: { exclude: ['password', 'otp_code', 'otp_expires_at'] }
  });

  if (!user) {
    throw createError(404, 'المستخدم غير موجود', 'غير موجود');
  }

  // التحقق من الصلاحيات
  if (req.user.user_type === 'citizen' && req.user.id !== id) {
    throw createError(403, 'ليس لديك صلاحية للوصول إلى هذا المستخدم', 'ممنوع');
  }

  res.json({
    success: true,
    data: { user }
  });
});

// إنشاء مستخدم جديد
const createUser = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const {
    full_name,
    phone_number,
    email,
    user_type,
    password,
    national_id,
    address,
    date_of_birth
  } = req.body;

  const user = await User.create({
    full_name,
    phone_number,
    email,
    user_type,
    password,
    national_id,
    address,
    date_of_birth,
    is_verified: user_type !== 'citizen' // الموظفون والإدارة محققون تلقائياً
  });

  res.status(201).json({
    success: true,
    message: 'تم إنشاء المستخدم بنجاح',
    data: {
      user: {
        id: user.id,
        full_name: user.full_name,
        phone_number: user.phone_number,
        email: user.email,
        user_type: user.user_type,
        is_active: user.is_active,
        is_verified: user.is_verified
      }
    }
  });
});

// تحديث مستخدم
const updateUser = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { id } = req.params;
  const { full_name, email, address, date_of_birth } = req.body;

  const user = await User.findByPk(id);
  if (!user) {
    throw createError(404, 'المستخدم غير موجود', 'غير موجود');
  }

  // التحقق من الصلاحيات
  if (req.user.user_type === 'citizen' && req.user.id !== id) {
    throw createError(403, 'يمكنك تعديل ملفك الشخصي فقط', 'ممنوع');
  }

  // تحديث البيانات
  if (full_name) user.full_name = full_name;
  if (email) user.email = email;
  if (address) user.address = address;
  if (date_of_birth) user.date_of_birth = date_of_birth;

  await user.save();

  res.json({
    success: true,
    message: 'تم تحديث المستخدم بنجاح',
    data: {
      user: {
        id: user.id,
        full_name: user.full_name,
        phone_number: user.phone_number,
        email: user.email,
        address: user.address,
        date_of_birth: user.date_of_birth
      }
    }
  });
});

// حذف مستخدم
const deleteUser = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await User.findByPk(id);
  if (!user) {
    throw createError(404, 'المستخدم غير موجود', 'غير موجود');
  }

  await user.destroy();

  res.json({
    success: true,
    message: 'تم حذف المستخدم بنجاح'
  });
});

// تفعيل/إلغاء تفعيل مستخدم
const toggleUserStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await User.findByPk(id);
  if (!user) {
    throw createError(404, 'المستخدم غير موجود', 'غير موجود');
  }

  user.is_active = !user.is_active;
  await user.save();

  res.json({
    success: true,
    message: `تم ${user.is_active ? 'تفعيل' : 'إلغاء تفعيل'} المستخدم بنجاح`,
    data: { user: { id: user.id, is_active: user.is_active } }
  });
});

// رفع صورة الملف الشخصي
const uploadProfileImage = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق رفع الصور لاحقاً'
  });
});

// حذف صورة الملف الشخصي
const deleteProfileImage = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق حذف الصور لاحقاً'
  });
});

// الحصول على إحصائيات المستخدمين
const getUserStats = asyncHandler(async (req, res) => {
  const totalUsers = await User.count();
  const citizenCount = await User.count({ where: { user_type: 'citizen' } });
  const employeeCount = await User.count({ where: { user_type: 'employee' } });
  const adminCount = await User.count({ where: { user_type: 'admin' } });
  const activeUsers = await User.count({ where: { is_active: true } });
  const verifiedUsers = await User.count({ where: { is_verified: true } });

  res.json({
    success: true,
    data: {
      total_users: totalUsers,
      citizen_count: citizenCount,
      employee_count: employeeCount,
      admin_count: adminCount,
      active_users: activeUsers,
      verified_users: verifiedUsers
    }
  });
});

// الحصول على طلبات المستخدم
const getUserRequests = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق هذه الوظيفة لاحقاً',
    data: { requests: [] }
  });
});

// الحصول على إشعارات المستخدم
const getUserNotifications = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق هذه الوظيفة لاحقاً',
    data: { notifications: [] }
  });
});

module.exports = {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  toggleUserStatus,
  uploadProfileImage,
  deleteProfileImage,
  getUserStats,
  getUserRequests,
  getUserNotifications
};
