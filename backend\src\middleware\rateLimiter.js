const rateLimit = require('express-rate-limit');

// محدد المعدل العام
const generalLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 دقيقة
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 100 طلب لكل نافذة زمنية
  message: {
    success: false,
    error: 'تم تجاوز الحد المسموح من الطلبات',
    message: 'لقد تجاوزت الحد المسموح من الطلبات، يرجى المحاولة لاحقاً',
    retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000) / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: () => process.env.NODE_ENV === 'development'
});

// محدد معدل صارم لتسجيل الدخول
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 5, // 5 محاولات تسجيل دخول لكل نافذة زمنية
  message: {
    success: false,
    error: 'تم تجاوز محاولات تسجيل الدخول',
    message: 'لقد تجاوزت الحد المسموح من محاولات تسجيل الدخول، يرجى المحاولة بعد 15 دقيقة',
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // لا تحسب الطلبات الناجحة
  skip: () => process.env.NODE_ENV === 'development'
});

// محدد معدل لإرسال OTP
const otpLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 دقائق
  max: 3, // 3 محاولات إرسال OTP لكل نافذة زمنية
  message: {
    success: false,
    error: 'تم تجاوز محاولات إرسال رمز التحقق',
    message: 'لقد تجاوزت الحد المسموح من محاولات إرسال رمز التحقق، يرجى المحاولة بعد 5 دقائق',
    retryAfter: 5 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: () => process.env.NODE_ENV === 'development'
});

// محدد معدل لرفع الملفات
const uploadLimiter = rateLimit({
  windowMs: 60 * 1000, // دقيقة واحدة
  max: 10, // 10 ملفات لكل دقيقة
  message: {
    success: false,
    error: 'تم تجاوز حد رفع الملفات',
    message: 'لقد تجاوزت الحد المسموح من رفع الملفات، يرجى المحاولة بعد دقيقة',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: () => process.env.NODE_ENV === 'development'
});

// محدد معدل لإنشاء الطلبات
const requestCreationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // ساعة واحدة
  max: 10, // 10 طلبات لكل ساعة
  message: {
    success: false,
    error: 'تم تجاوز حد إنشاء الطلبات',
    message: 'لقد تجاوزت الحد المسموح من إنشاء الطلبات، يرجى المحاولة بعد ساعة',
    retryAfter: 60 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: () => process.env.NODE_ENV === 'development'
});

// محدد معدل للتقارير
const reportLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // ساعة واحدة
  max: 5, // 5 تقارير لكل ساعة
  message: {
    success: false,
    error: 'تم تجاوز حد إنشاء التقارير',
    message: 'لقد تجاوزت الحد المسموح من إنشاء التقارير، يرجى المحاولة بعد ساعة',
    retryAfter: 60 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: () => process.env.NODE_ENV === 'development'
});

module.exports = {
  generalLimiter,
  loginLimiter,
  otpLimiter,
  uploadLimiter,
  requestCreationLimiter,
  reportLimiter
};
