const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const path = require('path');
const fs = require('fs').promises;

const Image = sequelize.define('Image', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  
  // معرف الطلب المرتبط
  request_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'requests',
      key: 'id'
    }
  },
  
  // اسم الملف الأصلي
  original_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  
  // اسم الملف المحفوظ
  filename: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  
  // مسار الملف
  file_path: {
    type: DataTypes.STRING(500),
    allowNull: false
  },
  
  // نوع الملف
  mime_type: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  
  // حجم الملف بالبايت
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 0
    }
  },
  
  // نوع المرفق
  attachment_type: {
    type: DataTypes.ENUM(
      'identity_document',      // وثيقة هوية
      'proof_of_residence',     // إثبات إقامة
      'income_proof',           // إثبات دخل
      'medical_report',         // تقرير طبي
      'academic_certificate',   // شهادة أكاديمية
      'employment_letter',      // خطاب عمل
      'marriage_certificate',   // شهادة زواج
      'birth_certificate',      // شهادة ميلاد
      'death_certificate',      // شهادة وفاة
      'court_document',         // وثيقة محكمة
      'bank_statement',         // كشف حساب بنكي
      'utility_bill',           // فاتورة خدمات
      'passport_copy',          // نسخة جواز سفر
      'driving_license',        // رخصة قيادة
      'other'                   // أخرى
    ),
    defaultValue: 'other',
    allowNull: false
  },
  
  // وصف المرفق
  description: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  
  // هل الملف مطلوب؟
  is_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  
  // حالة التحقق من الملف
  verification_status: {
    type: DataTypes.ENUM('pending', 'verified', 'rejected'),
    defaultValue: 'pending',
    allowNull: false
  },
  
  // ملاحظات التحقق
  verification_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  // معرف الموظف الذي تحقق من الملف
  verified_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  // تاريخ التحقق
  verified_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  // معلومات إضافية (JSON)
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'images',
  indexes: [
    {
      fields: ['request_id']
    },
    {
      fields: ['attachment_type']
    },
    {
      fields: ['verification_status']
    },
    {
      fields: ['verified_by']
    },
    {
      unique: true,
      fields: ['filename']
    }
  ]
});

// طرق مساعدة
Image.prototype.getFullPath = function() {
  return path.join(process.cwd(), this.file_path);
};

Image.prototype.getPublicUrl = function() {
  return `/uploads/${this.filename}`;
};

Image.prototype.isImage = function() {
  return this.mime_type.startsWith('image/');
};

Image.prototype.isPDF = function() {
  return this.mime_type === 'application/pdf';
};

Image.prototype.getFileExtension = function() {
  return path.extname(this.original_name).toLowerCase();
};

Image.prototype.getSizeInMB = function() {
  return (this.file_size / (1024 * 1024)).toFixed(2);
};

// حذف الملف من النظام عند حذف السجل
Image.beforeDestroy(async (image) => {
  try {
    const fullPath = image.getFullPath();
    await fs.unlink(fullPath);
    console.log(`تم حذف الملف: ${fullPath}`);
  } catch (error) {
    console.error(`خطأ في حذف الملف: ${error.message}`);
  }
});

module.exports = Image;
