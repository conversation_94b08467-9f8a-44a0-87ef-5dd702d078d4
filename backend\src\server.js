const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const path = require('path');
require('dotenv').config();

// استيراد قاعدة البيانات والنماذج
const { testConnection, syncDatabase, seedDatabase } = require('./models');

// استيراد المسارات
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const requestRoutes = require('./routes/requests');
const imageRoutes = require('./routes/images');
const notificationRoutes = require('./routes/notifications');
const reportRoutes = require('./routes/reports');

// استيراد الوسطاء
const errorHandler = require('./middleware/errorHandler');
const rateLimiter = require('./middleware/rateLimiter');

const app = express();
const PORT = process.env.PORT || 5000;

// إعداد الوسطاء الأساسية
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(compression());
app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// تطبيق محدد المعدل
app.use(rateLimiter);

// خدمة الملفات الثابتة
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// مسار الصحة
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// مسار الجذر
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في منصة المواطن - Citizen Platform API',
    version: '1.0.0',
    documentation: '/api/docs',
    health: '/health'
  });
});

// المسارات الرئيسية
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/requests', requestRoutes);
app.use('/api/images', imageRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/reports', reportRoutes);

// معالج الأخطاء 404
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود',
    path: req.originalUrl
  });
});

// معالج الأخطاء العام
app.use(errorHandler);

// دالة بدء الخادم
const startServer = async () => {
  try {
    console.log('🚀 بدء تشغيل خادم منصة المواطن...');
    
    // اختبار الاتصال بقاعدة البيانات
    await testConnection();
    
    // مزامنة قاعدة البيانات
    const shouldForceSync = process.env.NODE_ENV === 'development' && process.argv.includes('--force-sync');
    await syncDatabase(shouldForceSync);
    
    // إنشاء البيانات الأولية
    if (process.env.NODE_ENV === 'development' || process.argv.includes('--seed')) {
      await seedDatabase();
    }
    
    // بدء الخادم
    app.listen(PORT, () => {
      console.log(`🎉 الخادم يعمل على المنفذ ${PORT}`);
      console.log(`🌐 الرابط: http://localhost:${PORT}`);
      console.log(`📚 التوثيق: http://localhost:${PORT}/api/docs`);
      console.log(`💚 الصحة: http://localhost:${PORT}/health`);
      
      if (process.env.NODE_ENV === 'development') {
        console.log('\n📋 معلومات التطوير:');
        console.log('- المستخدم الإداري: <EMAIL> / Admin@123456');
        console.log('- الموظف التجريبي: <EMAIL> / Employee@123456');
        console.log('- لإعادة إنشاء قاعدة البيانات: npm run dev -- --force-sync');
        console.log('- لإنشاء البيانات الأولية: npm run dev -- --seed');
      }
    });
    
  } catch (error) {
    console.error('❌ خطأ في بدء الخادم:', error);
    process.exit(1);
  }
};

// معالجة الإغلاق الآمن
process.on('SIGTERM', () => {
  console.log('🛑 تم استلام إشارة SIGTERM، إغلاق الخادم...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 تم استلام إشارة SIGINT، إغلاق الخادم...');
  process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ رفض غير معالج:', reason);
  console.error('في:', promise);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ استثناء غير معالج:', error);
  process.exit(1);
});

// بدء الخادم
startServer();

module.exports = app;
