const { validationResult } = require('express-validator');
const { Notification, User, Request } = require('../models');
const { createError, asyncHandler } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

// الحصول على إشعارات المستخدم الحالي
const getUserNotifications = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const {
    page = 1,
    limit = 20,
    is_read,
    type,
    priority,
    date_from,
    date_to
  } = req.query;

  const offset = (page - 1) * limit;
  const where = { user_id: req.user.id };

  // فلترة حسب حالة القراءة
  if (is_read !== undefined) {
    where.is_read = is_read === 'true';
  }

  // فلترة حسب النوع
  if (type) {
    where.type = type;
  }

  // فلترة حسب الأولوية
  if (priority) {
    where.priority = priority;
  }

  // فلترة حسب التاريخ
  if (date_from || date_to) {
    where.created_at = {};
    if (date_from) {
      where.created_at[Op.gte] = new Date(date_from);
    }
    if (date_to) {
      where.created_at[Op.lte] = new Date(date_to);
    }
  }

  // إزالة الإشعارات المنتهية الصلاحية
  where[Op.or] = [
    { expires_at: null },
    { expires_at: { [Op.gt]: new Date() } }
  ];

  const { count, rows: notifications } = await Notification.findAndCountAll({
    where,
    include: [
      {
        model: Request,
        as: 'request',
        attributes: ['id', 'request_number', 'title'],
        required: false
      }
    ],
    limit: parseInt(limit),
    offset,
    order: [['created_at', 'DESC']]
  });

  res.json({
    success: true,
    data: {
      notifications,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: parseInt(limit)
      }
    }
  });
});

// إنشاء إشعار جديد
const createNotification = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const {
    user_id,
    request_id,
    type,
    title,
    message,
    priority = 'medium',
    delivery_methods = { push: true, email: false, sms: false },
    expires_at,
    action_url,
    action_text
  } = req.body;

  const notification = await Notification.create({
    user_id,
    request_id,
    type,
    title,
    message,
    priority,
    delivery_methods,
    expires_at,
    action_url,
    action_text
  });

  res.status(201).json({
    success: true,
    message: 'تم إنشاء الإشعار بنجاح',
    data: { notification }
  });
});

// إرسال إشعار جماعي
const broadcastNotification = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'سيتم تطبيق الإشعارات الجماعية لاحقاً'
  });
});

// الحصول على إشعار محدد
const getNotificationById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const notification = await Notification.findByPk(id, {
    include: [
      {
        model: Request,
        as: 'request',
        attributes: ['id', 'request_number', 'title'],
        required: false
      }
    ]
  });

  if (!notification) {
    throw createError(404, 'الإشعار غير موجود', 'غير موجود');
  }

  res.json({
    success: true,
    data: { notification }
  });
});

// تحديث إشعار
const updateNotification = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { id } = req.params;
  const { title, message, priority, expires_at } = req.body;

  const notification = await Notification.findByPk(id);
  if (!notification) {
    throw createError(404, 'الإشعار غير موجود', 'غير موجود');
  }

  // تحديث البيانات
  if (title) notification.title = title;
  if (message) notification.message = message;
  if (priority) notification.priority = priority;
  if (expires_at !== undefined) notification.expires_at = expires_at;

  await notification.save();

  res.json({
    success: true,
    message: 'تم تحديث الإشعار بنجاح',
    data: { notification }
  });
});

// حذف إشعار
const deleteNotification = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const notification = await Notification.findByPk(id);
  if (!notification) {
    throw createError(404, 'الإشعار غير موجود', 'غير موجود');
  }

  await notification.destroy();

  res.json({
    success: true,
    message: 'تم حذف الإشعار بنجاح'
  });
});

// تحديد إشعار كمقروء
const markAsRead = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const notification = await Notification.findByPk(id);
  if (!notification) {
    throw createError(404, 'الإشعار غير موجود', 'غير موجود');
  }

  await notification.markAsRead();

  res.json({
    success: true,
    message: 'تم تحديد الإشعار كمقروء',
    data: { notification }
  });
});

// تحديد إشعار كغير مقروء
const markAsUnread = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const notification = await Notification.findByPk(id);
  if (!notification) {
    throw createError(404, 'الإشعار غير موجود', 'غير موجود');
  }

  notification.is_read = false;
  notification.read_at = null;
  await notification.save();

  res.json({
    success: true,
    message: 'تم تحديد الإشعار كغير مقروء',
    data: { notification }
  });
});

// تحديد جميع الإشعارات كمقروءة
const markAllAsRead = asyncHandler(async (req, res) => {
  await Notification.update(
    { is_read: true, read_at: new Date() },
    { where: { user_id: req.user.id, is_read: false } }
  );

  res.json({
    success: true,
    message: 'تم تحديد جميع الإشعارات كمقروءة'
  });
});

// حذف جميع الإشعارات المقروءة
const clearReadNotifications = asyncHandler(async (req, res) => {
  const deletedCount = await Notification.destroy({
    where: { user_id: req.user.id, is_read: true }
  });

  res.json({
    success: true,
    message: `تم حذف ${deletedCount} إشعار مقروء`
  });
});

// الحصول على عدد الإشعارات غير المقروءة
const getUnreadCount = asyncHandler(async (req, res) => {
  const unreadCount = await Notification.count({
    where: {
      user_id: req.user.id,
      is_read: false,
      [Op.or]: [
        { expires_at: null },
        { expires_at: { [Op.gt]: new Date() } }
      ]
    }
  });

  res.json({
    success: true,
    data: { unread_count: unreadCount }
  });
});

// الحصول على إحصائيات الإشعارات
const getNotificationStats = asyncHandler(async (req, res) => {
  const totalNotifications = await Notification.count();
  const readNotifications = await Notification.count({ where: { is_read: true } });
  const unreadNotifications = await Notification.count({ where: { is_read: false } });

  res.json({
    success: true,
    data: {
      total_notifications: totalNotifications,
      read_notifications: readNotifications,
      unread_notifications: unreadNotifications
    }
  });
});

module.exports = {
  getUserNotifications,
  createNotification,
  broadcastNotification,
  getNotificationById,
  updateNotification,
  deleteNotification,
  markAsRead,
  markAsUnread,
  markAllAsRead,
  clearReadNotifications,
  getUnreadCount,
  getNotificationStats
};
