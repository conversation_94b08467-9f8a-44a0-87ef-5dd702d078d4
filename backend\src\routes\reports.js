const express = require('express');
const { body, query } = require('express-validator');
const reportController = require('../controllers/reportController');
const { 
  authenticate, 
  employeeOnly, 
  adminOnly 
} = require('../middleware/auth');
const { reportLimiter } = require('../middleware/rateLimiter');

const router = express.Router();

// جميع المسارات تتطلب مصادقة
router.use(authenticate);

// الحصول على قائمة التقارير (للموظفين والإدارة فقط)
router.get('/', employeeOnly, [
  query('report_type')
    .optional()
    .isIn(['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom'])
    .withMessage('نوع التقرير غير صحيح'),
  query('status')
    .optional()
    .isIn(['generating', 'completed', 'failed'])
    .withMessage('حالة التقرير غير صحيحة'),
  query('is_public')
    .optional()
    .isBoolean()
    .withMessage('حالة العمومية يجب أن تكون true أو false'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 50'),
  query('date_from')
    .optional()
    .isISO8601()
    .withMessage('تاريخ البداية غير صحيح'),
  query('date_to')
    .optional()
    .isISO8601()
    .withMessage('تاريخ النهاية غير صحيح')
], reportController.getReports);

// إنشاء تقرير جديد (للموظفين والإدارة فقط)
router.post('/', employeeOnly, reportLimiter, [
  body('report_type')
    .isIn(['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom'])
    .withMessage('نوع التقرير غير صحيح'),
  body('title')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('عنوان التقرير يجب أن يكون بين 5 و 200 حرف'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('وصف التقرير يجب أن يكون أقل من 1000 حرف'),
  body('period_start')
    .isISO8601()
    .withMessage('تاريخ بداية الفترة غير صحيح'),
  body('period_end')
    .isISO8601()
    .withMessage('تاريخ نهاية الفترة غير صحيح')
    .custom((value, { req }) => {
      if (new Date(value) <= new Date(req.body.period_start)) {
        throw new Error('تاريخ نهاية الفترة يجب أن يكون بعد تاريخ البداية');
      }
      return true;
    }),
  body('is_public')
    .optional()
    .isBoolean()
    .withMessage('حالة العمومية يجب أن تكون true أو false'),
  body('metadata')
    .optional()
    .isObject()
    .withMessage('البيانات الوصفية يجب أن تكون كائن JSON')
], reportController.createReport);

// إنشاء تقرير شهري تلقائي (للإدارة فقط)
router.post('/auto/monthly', adminOnly, [
  body('year')
    .isInt({ min: 2020, max: 2030 })
    .withMessage('السنة غير صحيحة'),
  body('month')
    .isInt({ min: 1, max: 12 })
    .withMessage('الشهر غير صحيح')
], reportController.createMonthlyReport);

// إنشاء تقرير سنوي تلقائي (للإدارة فقط)
router.post('/auto/yearly', adminOnly, [
  body('year')
    .isInt({ min: 2020, max: 2030 })
    .withMessage('السنة غير صحيحة')
], reportController.createYearlyReport);

// الحصول على تقرير محدد
router.get('/:id', employeeOnly, reportController.getReportById);

// تحديث تقرير
router.put('/:id', employeeOnly, [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('عنوان التقرير يجب أن يكون بين 5 و 200 حرف'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('وصف التقرير يجب أن يكون أقل من 1000 حرف'),
  body('is_public')
    .optional()
    .isBoolean()
    .withMessage('حالة العمومية يجب أن تكون true أو false'),
  body('metadata')
    .optional()
    .isObject()
    .withMessage('البيانات الوصفية يجب أن تكون كائن JSON')
], reportController.updateReport);

// حذف تقرير (للإدارة فقط)
router.delete('/:id', adminOnly, reportController.deleteReport);

// تحميل تقرير (PDF)
router.get('/:id/download', employeeOnly, reportController.downloadReport);

// عرض تقرير في المتصفح
router.get('/:id/view', employeeOnly, reportController.viewReport);

// إعادة إنشاء تقرير
router.post('/:id/regenerate', employeeOnly, reportController.regenerateReport);

// الحصول على إحصائيات التقارير (للإدارة فقط)
router.get('/stats/overview', adminOnly, [
  query('period')
    .optional()
    .isIn(['today', 'week', 'month', 'quarter', 'year'])
    .withMessage('فترة الإحصائيات غير صحيحة'),
  query('report_type')
    .optional()
    .isIn(['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom'])
    .withMessage('نوع التقرير غير صحيح')
], reportController.getReportStats);

// الحصول على التقارير العامة
router.get('/public/list', [
  query('report_type')
    .optional()
    .isIn(['monthly', 'quarterly', 'yearly'])
    .withMessage('نوع التقرير غير صحيح'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 20')
], reportController.getPublicReports);

// الحصول على إحصائيات سريعة للوحة التحكم
router.get('/dashboard/quick-stats', employeeOnly, reportController.getDashboardStats);

// تصدير بيانات التقرير (JSON/CSV)
router.get('/:id/export', employeeOnly, [
  query('format')
    .optional()
    .isIn(['json', 'csv'])
    .withMessage('تنسيق التصدير يجب أن يكون json أو csv')
], reportController.exportReportData);

// جدولة تقرير دوري (للإدارة فقط)
router.post('/schedule', adminOnly, [
  body('report_type')
    .isIn(['daily', 'weekly', 'monthly', 'quarterly', 'yearly'])
    .withMessage('نوع التقرير غير صحيح'),
  body('title_template')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('قالب عنوان التقرير يجب أن يكون بين 5 و 200 حرف'),
  body('is_public')
    .optional()
    .isBoolean()
    .withMessage('حالة العمومية يجب أن تكون true أو false'),
  body('auto_generate')
    .optional()
    .isBoolean()
    .withMessage('حالة الإنشاء التلقائي يجب أن تكون true أو false')
], reportController.scheduleReport);

module.exports = router;
