const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  
  // بيانات المواطن الأساسية
  full_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 100]
    }
  },
  
  phone_number: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      is: /^[+]?[0-9\s\-\(\)]+$/
    }
  },
  
  email: {
    type: DataTypes.STRING(100),
    allowNull: true,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  
  national_id: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true
  },
  
  // معلومات إضافية
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  date_of_birth: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  
  // نوع المستخدم (مواطن أو موظف)
  user_type: {
    type: DataTypes.ENUM('citizen', 'employee', 'admin'),
    defaultValue: 'citizen',
    allowNull: false
  },
  
  // كلمة المرور (للموظفين والإدارة)
  password: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  
  // حالة الحساب
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  
  is_verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  
  // OTP للتحقق
  otp_code: {
    type: DataTypes.STRING(6),
    allowNull: true
  },
  
  otp_expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  // معلومات إضافية
  last_login: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  profile_image: {
    type: DataTypes.STRING(255),
    allowNull: true
  }
}, {
  tableName: 'users',
  indexes: [
    {
      unique: true,
      fields: ['phone_number']
    },
    {
      unique: true,
      fields: ['email']
    },
    {
      fields: ['user_type']
    },
    {
      fields: ['is_active']
    }
  ]
});

// تشفير كلمة المرور قبل الحفظ
User.beforeCreate(async (user) => {
  if (user.password) {
    const salt = await bcrypt.genSalt(12);
    user.password = await bcrypt.hash(user.password, salt);
  }
});

User.beforeUpdate(async (user) => {
  if (user.changed('password') && user.password) {
    const salt = await bcrypt.genSalt(12);
    user.password = await bcrypt.hash(user.password, salt);
  }
});

// طرق مساعدة
User.prototype.comparePassword = async function(candidatePassword) {
  if (!this.password) return false;
  return await bcrypt.compare(candidatePassword, this.password);
};

User.prototype.generateOTP = function() {
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  this.otp_code = otp;
  this.otp_expires_at = new Date(Date.now() + parseInt(process.env.OTP_EXPIRES_IN || 300000));
  return otp;
};

User.prototype.verifyOTP = function(otp) {
  return this.otp_code === otp && this.otp_expires_at > new Date();
};

module.exports = User;
