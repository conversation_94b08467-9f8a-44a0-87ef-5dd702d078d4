const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Report = sequelize.define('Report', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  
  // نوع التقرير
  report_type: {
    type: DataTypes.ENUM(
      'monthly',        // شهري
      'quarterly',      // ربع سنوي
      'yearly',         // سنوي
      'custom',         // مخصص
      'daily',          // يومي
      'weekly'          // أسبوعي
    ),
    allowNull: false
  },
  
  // عنوان التقرير
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [5, 200]
    }
  },
  
  // وصف التقرير
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  // فترة التقرير
  period_start: {
    type: DataTypes.DATE,
    allowNull: false
  },
  
  period_end: {
    type: DataTypes.DATE,
    allowNull: false
  },
  
  // معرف المستخدم الذي أنشأ التقرير
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  // حالة التقرير
  status: {
    type: DataTypes.ENUM('generating', 'completed', 'failed'),
    defaultValue: 'generating',
    allowNull: false
  },
  
  // بيانات التقرير (JSON)
  data: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  },
  
  // إحصائيات التقرير
  statistics: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {
      total_requests: 0,
      completed_requests: 0,
      pending_requests: 0,
      rejected_requests: 0,
      cancelled_requests: 0,
      average_processing_time: 0,
      requests_by_type: {},
      requests_by_status: {},
      monthly_trends: [],
      user_satisfaction: {
        average_rating: 0,
        total_ratings: 0,
        rating_distribution: {}
      }
    }
  },
  
  // مسار ملف التقرير (PDF)
  file_path: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  
  // حجم الملف
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  
  // تاريخ انتهاء صلاحية التقرير
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  // هل التقرير عام؟
  is_public: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  
  // معلومات إضافية
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'reports',
  indexes: [
    {
      fields: ['report_type']
    },
    {
      fields: ['created_by']
    },
    {
      fields: ['status']
    },
    {
      fields: ['period_start', 'period_end']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['is_public']
    }
  ]
});

// طرق مساعدة
Report.prototype.isExpired = function() {
  return this.expires_at && this.expires_at < new Date();
};

Report.prototype.getFileUrl = function() {
  if (!this.file_path) return null;
  return `/reports/${this.id}/download`;
};

Report.prototype.calculateStatistics = async function() {
  const { Request, User } = require('./index');
  const { Op } = require('sequelize');
  
  try {
    // إحصائيات الطلبات
    const totalRequests = await Request.count({
      where: {
        created_at: {
          [Op.between]: [this.period_start, this.period_end]
        }
      }
    });
    
    const requestsByStatus = await Request.findAll({
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        created_at: {
          [Op.between]: [this.period_start, this.period_end]
        }
      },
      group: ['status'],
      raw: true
    });
    
    const requestsByType = await Request.findAll({
      attributes: [
        'request_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        created_at: {
          [Op.between]: [this.period_start, this.period_end]
        }
      },
      group: ['request_type'],
      raw: true
    });
    
    // حساب متوسط وقت المعالجة
    const completedRequests = await Request.findAll({
      where: {
        status: 'completed',
        created_at: {
          [Op.between]: [this.period_start, this.period_end]
        },
        actual_completion_date: {
          [Op.not]: null
        }
      },
      attributes: ['created_at', 'actual_completion_date']
    });
    
    let averageProcessingTime = 0;
    if (completedRequests.length > 0) {
      const totalProcessingTime = completedRequests.reduce((sum, request) => {
        const processingTime = new Date(request.actual_completion_date) - new Date(request.created_at);
        return sum + processingTime;
      }, 0);
      averageProcessingTime = Math.round(totalProcessingTime / completedRequests.length / (1000 * 60 * 60 * 24)); // أيام
    }
    
    // تحديث الإحصائيات
    this.statistics = {
      total_requests: totalRequests,
      completed_requests: requestsByStatus.find(r => r.status === 'completed')?.count || 0,
      pending_requests: requestsByStatus.find(r => r.status === 'pending')?.count || 0,
      rejected_requests: requestsByStatus.find(r => r.status === 'rejected')?.count || 0,
      cancelled_requests: requestsByStatus.find(r => r.status === 'cancelled')?.count || 0,
      average_processing_time: averageProcessingTime,
      requests_by_type: requestsByType.reduce((acc, item) => {
        acc[item.request_type] = parseInt(item.count);
        return acc;
      }, {}),
      requests_by_status: requestsByStatus.reduce((acc, item) => {
        acc[item.status] = parseInt(item.count);
        return acc;
      }, {})
    };
    
    await this.save();
    return this.statistics;
  } catch (error) {
    console.error('خطأ في حساب إحصائيات التقرير:', error);
    throw error;
  }
};

module.exports = Report;
