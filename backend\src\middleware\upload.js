const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const sharp = require('sharp');

// إعداد التخزين
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../../uploads');
    
    try {
      await fs.access(uploadPath);
    } catch (error) {
      await fs.mkdir(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // إنشاء اسم ملف فريد
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// فلتر أنواع الملفات المسموحة
const fileFilter = (req, file, cb) => {
  const allowedTypes = process.env.ALLOWED_FILE_TYPES?.split(',') || [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    const error = new Error('نوع الملف غير مدعوم');
    error.code = 'INVALID_FILE_TYPE';
    cb(error, false);
  }
};

// إعداد multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB
    files: 10 // حد أقصى 10 ملفات
  }
});

// وسيط لرفع ملف واحد
const uploadSingle = (fieldName = 'file') => {
  return upload.single(fieldName);
};

// وسيط لرفع ملفات متعددة
const uploadMultiple = (fieldName = 'files', maxCount = 10) => {
  return upload.array(fieldName, maxCount);
};

// وسيط لمعالجة الصور
const processImage = async (req, res, next) => {
  if (!req.file || !req.file.mimetype.startsWith('image/')) {
    return next();
  }

  try {
    const inputPath = req.file.path;
    const outputPath = path.join(
      path.dirname(inputPath),
      `processed_${req.file.filename}`
    );

    // معالجة الصورة: تحسين الجودة وتقليل الحجم
    await sharp(inputPath)
      .resize(1920, 1080, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .jpeg({ 
        quality: 85,
        progressive: true 
      })
      .toFile(outputPath);

    // استبدال الملف الأصلي بالملف المعالج
    await fs.unlink(inputPath);
    await fs.rename(outputPath, inputPath);

    // تحديث معلومات الملف
    const stats = await fs.stat(inputPath);
    req.file.size = stats.size;

    next();
  } catch (error) {
    console.error('خطأ في معالجة الصورة:', error);
    next(); // متابعة حتى لو فشلت المعالجة
  }
};

// وسيط لمعالجة ملفات متعددة
const processMultipleImages = async (req, res, next) => {
  if (!req.files || req.files.length === 0) {
    return next();
  }

  try {
    const processPromises = req.files
      .filter(file => file.mimetype.startsWith('image/'))
      .map(async (file) => {
        const inputPath = file.path;
        const outputPath = path.join(
          path.dirname(inputPath),
          `processed_${file.filename}`
        );

        await sharp(inputPath)
          .resize(1920, 1080, { 
            fit: 'inside',
            withoutEnlargement: true 
          })
          .jpeg({ 
            quality: 85,
            progressive: true 
          })
          .toFile(outputPath);

        await fs.unlink(inputPath);
        await fs.rename(outputPath, inputPath);

        const stats = await fs.stat(inputPath);
        file.size = stats.size;
      });

    await Promise.all(processPromises);
    next();
  } catch (error) {
    console.error('خطأ في معالجة الصور:', error);
    next();
  }
};

// وسيط للتحقق من صحة الملف
const validateFile = (req, res, next) => {
  if (!req.file && !req.files) {
    return res.status(400).json({
      success: false,
      error: 'لم يتم رفع أي ملف',
      message: 'يرجى اختيار ملف للرفع'
    });
  }

  // التحقق من حجم الملف
  const maxSize = parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024;
  const files = req.files || [req.file];

  for (const file of files) {
    if (file.size > maxSize) {
      return res.status(400).json({
        success: false,
        error: 'حجم الملف كبير جداً',
        message: `حجم الملف ${file.originalname} يتجاوز الحد المسموح (${Math.round(maxSize / 1024 / 1024)} ميجابايت)`
      });
    }
  }

  next();
};

// دالة لحذف الملف
const deleteFile = async (filePath) => {
  try {
    await fs.unlink(filePath);
    console.log(`تم حذف الملف: ${filePath}`);
  } catch (error) {
    console.error(`خطأ في حذف الملف ${filePath}:`, error);
  }
};

// دالة للحصول على معلومات الملف
const getFileInfo = (file) => {
  return {
    originalName: file.originalname,
    filename: file.filename,
    path: file.path,
    size: file.size,
    mimeType: file.mimetype,
    extension: path.extname(file.originalname).toLowerCase()
  };
};

// معالج أخطاء رفع الملفات
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'حجم الملف كبير جداً',
        message: 'الحد الأقصى لحجم الملف هو 5 ميجابايت'
      });
    }
    
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'عدد الملفات كبير جداً',
        message: 'الحد الأقصى هو 10 ملفات'
      });
    }
    
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        error: 'حقل الملف غير متوقع',
        message: 'اسم حقل الملف غير صحيح'
      });
    }
  }
  
  if (error.code === 'INVALID_FILE_TYPE') {
    return res.status(400).json({
      success: false,
      error: 'نوع الملف غير مدعوم',
      message: 'يرجى رفع ملفات من الأنواع المدعومة فقط (JPEG, PNG, GIF, PDF)'
    });
  }
  
  next(error);
};

module.exports = {
  upload,
  uploadSingle,
  uploadMultiple,
  processImage,
  processMultipleImages,
  validateFile,
  deleteFile,
  getFileInfo,
  handleUploadError
};
