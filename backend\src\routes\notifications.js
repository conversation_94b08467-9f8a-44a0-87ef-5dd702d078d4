const express = require('express');
const { body, query } = require('express-validator');
const notificationController = require('../controllers/notificationController');
const { 
  authenticate, 
  canAccessNotification, 
  employeeOnly,
  adminOnly 
} = require('../middleware/auth');

const router = express.Router();

// جميع المسارات تتطلب مصادقة
router.use(authenticate);

// الحصول على إشعارات المستخدم الحالي
router.get('/', [
  query('is_read')
    .optional()
    .isBoolean()
    .withMessage('حالة القراءة يجب أن تكون true أو false'),
  query('type')
    .optional()
    .isIn([
      'request_submitted', 'request_received', 'request_under_review',
      'request_in_progress', 'request_completed', 'request_rejected',
      'request_cancelled', 'document_required', 'document_verified',
      'document_rejected', 'appointment_scheduled', 'appointment_reminder',
      'payment_required', 'payment_received', 'system_maintenance',
      'general_announcement', 'other'
    ])
    .withMessage('نوع الإشعار غير صحيح'),
  query('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('أولوية الإشعار غير صحيحة'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('رقم الصفحة يجب أن يكون رقم صحيح أكبر من 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('حد النتائج يجب أن يكون بين 1 و 50'),
  query('date_from')
    .optional()
    .isISO8601()
    .withMessage('تاريخ البداية غير صحيح'),
  query('date_to')
    .optional()
    .isISO8601()
    .withMessage('تاريخ النهاية غير صحيح')
], notificationController.getUserNotifications);

// إنشاء إشعار جديد (للموظفين والإدارة فقط)
router.post('/', employeeOnly, [
  body('user_id')
    .optional()
    .isUUID()
    .withMessage('معرف المستخدم غير صحيح'),
  body('request_id')
    .optional()
    .isUUID()
    .withMessage('معرف الطلب غير صحيح'),
  body('type')
    .isIn([
      'request_submitted', 'request_received', 'request_under_review',
      'request_in_progress', 'request_completed', 'request_rejected',
      'request_cancelled', 'document_required', 'document_verified',
      'document_rejected', 'appointment_scheduled', 'appointment_reminder',
      'payment_required', 'payment_received', 'system_maintenance',
      'general_announcement', 'other'
    ])
    .withMessage('نوع الإشعار غير صحيح'),
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('عنوان الإشعار يجب أن يكون بين 1 و 200 حرف'),
  body('message')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('محتوى الإشعار يجب أن يكون بين 1 و 1000 حرف'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('أولوية الإشعار غير صحيحة'),
  body('delivery_methods')
    .optional()
    .isObject()
    .withMessage('طرق الإرسال يجب أن تكون كائن JSON'),
  body('expires_at')
    .optional()
    .isISO8601()
    .withMessage('تاريخ انتهاء الصلاحية غير صحيح'),
  body('action_url')
    .optional()
    .isURL()
    .withMessage('رابط الإجراء غير صحيح'),
  body('action_text')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('نص زر الإجراء يجب أن يكون أقل من 50 حرف')
], notificationController.createNotification);

// إرسال إشعار جماعي (للإدارة فقط)
router.post('/broadcast', adminOnly, [
  body('user_type')
    .optional()
    .isIn(['citizen', 'employee', 'admin'])
    .withMessage('نوع المستخدم غير صحيح'),
  body('user_ids')
    .optional()
    .isArray()
    .withMessage('معرفات المستخدمين يجب أن تكون مصفوفة'),
  body('user_ids.*')
    .optional()
    .isUUID()
    .withMessage('معرف المستخدم غير صحيح'),
  body('type')
    .isIn([
      'system_maintenance', 'general_announcement', 'other'
    ])
    .withMessage('نوع الإشعار غير صحيح للإرسال الجماعي'),
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('عنوان الإشعار يجب أن يكون بين 1 و 200 حرف'),
  body('message')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('محتوى الإشعار يجب أن يكون بين 1 و 1000 حرف'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('أولوية الإشعار غير صحيحة'),
  body('delivery_methods')
    .optional()
    .isObject()
    .withMessage('طرق الإرسال يجب أن تكون كائن JSON')
], notificationController.broadcastNotification);

// الحصول على إشعار محدد
router.get('/:id', canAccessNotification, notificationController.getNotificationById);

// تحديث إشعار
router.put('/:id', canAccessNotification, [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('عنوان الإشعار يجب أن يكون بين 1 و 200 حرف'),
  body('message')
    .optional()
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('محتوى الإشعار يجب أن يكون بين 1 و 1000 حرف'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('أولوية الإشعار غير صحيحة'),
  body('expires_at')
    .optional()
    .isISO8601()
    .withMessage('تاريخ انتهاء الصلاحية غير صحيح')
], notificationController.updateNotification);

// حذف إشعار
router.delete('/:id', canAccessNotification, notificationController.deleteNotification);

// تحديد إشعار كمقروء
router.patch('/:id/read', canAccessNotification, notificationController.markAsRead);

// تحديد إشعار كغير مقروء
router.patch('/:id/unread', canAccessNotification, notificationController.markAsUnread);

// تحديد جميع الإشعارات كمقروءة
router.patch('/mark-all/read', notificationController.markAllAsRead);

// حذف جميع الإشعارات المقروءة
router.delete('/read/clear', notificationController.clearReadNotifications);

// الحصول على عدد الإشعارات غير المقروءة
router.get('/count/unread', notificationController.getUnreadCount);

// الحصول على إحصائيات الإشعارات (للموظفين والإدارة فقط)
router.get('/stats/overview', employeeOnly, [
  query('period')
    .optional()
    .isIn(['today', 'week', 'month', 'quarter', 'year'])
    .withMessage('فترة الإحصائيات غير صحيحة'),
  query('type')
    .optional()
    .isIn([
      'request_submitted', 'request_received', 'request_under_review',
      'request_in_progress', 'request_completed', 'request_rejected',
      'request_cancelled', 'document_required', 'document_verified',
      'document_rejected', 'appointment_scheduled', 'appointment_reminder',
      'payment_required', 'payment_received', 'system_maintenance',
      'general_announcement', 'other'
    ])
    .withMessage('نوع الإشعار غير صحيح')
], notificationController.getNotificationStats);

module.exports = router;
