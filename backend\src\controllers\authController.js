const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');
const { User } = require('../models');
const { createError, asyncHandler } = require('../middleware/errorHandler');
const { sendSMS, sendEmail } = require('../services/notificationService');

// إنشاء رمز JWT
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  });
};

// تسجيل مستخدم جديد (مواطن)
const register = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { full_name, phone_number, email, national_id, address, date_of_birth } = req.body;

  // التحقق من عدم وجود المستخدم مسبقاً
  const existingUser = await User.findOne({
    where: { phone_number }
  });

  if (existingUser) {
    throw createError(400, 'رقم الهاتف مسجل مسبقاً', 'مستخدم موجود');
  }

  // إنشاء المستخدم الجديد
  const user = await User.create({
    full_name,
    phone_number,
    email,
    national_id,
    address,
    date_of_birth,
    user_type: 'citizen'
  });

  // إنشاء وإرسال رمز OTP
  const otp = user.generateOTP();
  await user.save();

  // إرسال رمز OTP عبر SMS
  try {
    await sendSMS(phone_number, `رمز التحقق الخاص بك في منصة المواطن: ${otp}`);
  } catch (error) {
    console.error('خطأ في إرسال SMS:', error);
    // لا نرمي خطأ هنا لأن التسجيل تم بنجاح
  }

  res.status(201).json({
    success: true,
    message: 'تم إنشاء الحساب بنجاح. يرجى التحقق من رقم الهاتف',
    data: {
      user_id: user.id,
      phone_number: user.phone_number,
      otp_sent: true
    }
  });
});

// إرسال رمز التحقق OTP
const sendOTP = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { phone_number } = req.body;

  const user = await User.findOne({ where: { phone_number } });
  if (!user) {
    throw createError(404, 'رقم الهاتف غير مسجل', 'مستخدم غير موجود');
  }

  // إنشاء رمز OTP جديد
  const otp = user.generateOTP();
  await user.save();

  // إرسال رمز OTP عبر SMS
  try {
    await sendSMS(phone_number, `رمز التحقق الخاص بك في منصة المواطن: ${otp}`);
  } catch (error) {
    console.error('خطأ في إرسال SMS:', error);
    throw createError(500, 'فشل في إرسال رمز التحقق', 'خطأ في الإرسال');
  }

  res.json({
    success: true,
    message: 'تم إرسال رمز التحقق بنجاح',
    data: {
      phone_number,
      expires_in: 5 // دقائق
    }
  });
});

// التحقق من رمز OTP وتسجيل الدخول
const verifyOTP = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { phone_number, otp_code } = req.body;

  const user = await User.findOne({ where: { phone_number } });
  if (!user) {
    throw createError(404, 'رقم الهاتف غير مسجل', 'مستخدم غير موجود');
  }

  if (!user.verifyOTP(otp_code)) {
    throw createError(400, 'رمز التحقق غير صحيح أو منتهي الصلاحية', 'OTP غير صحيح');
  }

  // تحديث حالة التحقق وآخر تسجيل دخول
  user.is_verified = true;
  user.last_login = new Date();
  user.otp_code = null;
  user.otp_expires_at = null;
  await user.save();

  // إنشاء رمز JWT
  const token = generateToken(user.id);

  res.json({
    success: true,
    message: 'تم تسجيل الدخول بنجاح',
    data: {
      token,
      user: {
        id: user.id,
        full_name: user.full_name,
        phone_number: user.phone_number,
        email: user.email,
        user_type: user.user_type,
        is_verified: user.is_verified
      }
    }
  });
});

// تسجيل دخول الموظفين والإدارة
const login = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { email, password } = req.body;

  const user = await User.findOne({ 
    where: { 
      email,
      user_type: ['employee', 'admin']
    }
  });

  if (!user || !(await user.comparePassword(password))) {
    throw createError(401, 'البريد الإلكتروني أو كلمة المرور غير صحيحة', 'بيانات خاطئة');
  }

  if (!user.is_active) {
    throw createError(401, 'الحساب غير نشط، يرجى التواصل مع الإدارة', 'حساب معطل');
  }

  // تحديث آخر تسجيل دخول
  user.last_login = new Date();
  await user.save();

  // إنشاء رمز JWT
  const token = generateToken(user.id);

  res.json({
    success: true,
    message: 'تم تسجيل الدخول بنجاح',
    data: {
      token,
      user: {
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        user_type: user.user_type,
        is_active: user.is_active
      }
    }
  });
});

// تسجيل الخروج
const logout = asyncHandler(async (req, res) => {
  // في التطبيق الحقيقي، يمكن إضافة الرمز إلى قائمة سوداء
  res.json({
    success: true,
    message: 'تم تسجيل الخروج بنجاح'
  });
});

// تحديث الرمز المميز
const refreshToken = asyncHandler(async (req, res) => {
  const token = generateToken(req.user.id);

  res.json({
    success: true,
    message: 'تم تحديث الرمز المميز بنجاح',
    data: { token }
  });
});

// التحقق من صحة الرمز المميز
const verifyToken = asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'الرمز المميز صحيح',
    data: {
      user: {
        id: req.user.id,
        full_name: req.user.full_name,
        phone_number: req.user.phone_number,
        email: req.user.email,
        user_type: req.user.user_type,
        is_verified: req.user.is_verified,
        is_active: req.user.is_active
      }
    }
  });
});

// الحصول على المستخدم الحالي
const getCurrentUser = asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.user.id, {
    attributes: { exclude: ['password', 'otp_code', 'otp_expires_at'] }
  });

  res.json({
    success: true,
    data: { user }
  });
});

// تحديث الملف الشخصي
const updateProfile = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { full_name, email, address, date_of_birth } = req.body;

  const user = await User.findByPk(req.user.id);
  
  if (full_name) user.full_name = full_name;
  if (email) user.email = email;
  if (address) user.address = address;
  if (date_of_birth) user.date_of_birth = date_of_birth;

  await user.save();

  res.json({
    success: true,
    message: 'تم تحديث الملف الشخصي بنجاح',
    data: {
      user: {
        id: user.id,
        full_name: user.full_name,
        phone_number: user.phone_number,
        email: user.email,
        address: user.address,
        date_of_birth: user.date_of_birth
      }
    }
  });
});

// تغيير كلمة المرور
const changePassword = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw createError(400, 'بيانات غير صحيحة', errors.array());
  }

  const { current_password, new_password } = req.body;

  const user = await User.findByPk(req.user.id);

  if (!(await user.comparePassword(current_password))) {
    throw createError(400, 'كلمة المرور الحالية غير صحيحة', 'كلمة مرور خاطئة');
  }

  user.password = new_password;
  await user.save();

  res.json({
    success: true,
    message: 'تم تغيير كلمة المرور بنجاح'
  });
});

// طلب إعادة تعيين كلمة المرور
const forgotPassword = asyncHandler(async (req, res) => {
  // سيتم تطبيقها لاحقاً
  res.json({
    success: true,
    message: 'سيتم إرسال رابط إعادة تعيين كلمة المرور قريباً'
  });
});

// إعادة تعيين كلمة المرور
const resetPassword = asyncHandler(async (req, res) => {
  // سيتم تطبيقها لاحقاً
  res.json({
    success: true,
    message: 'تم إعادة تعيين كلمة المرور بنجاح'
  });
});

module.exports = {
  register,
  sendOTP,
  verifyOTP,
  login,
  logout,
  refreshToken,
  verifyToken,
  getCurrentUser,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword
};
