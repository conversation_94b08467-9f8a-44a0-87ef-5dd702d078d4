const jwt = require('jsonwebtoken');
const { User } = require('../models');
const { createError, asyncHandler } = require('./errorHandler');

// التحقق من صحة الرمز المميز
const authenticate = asyncHandler(async (req, res, next) => {
  let token;

  // الحصول على الرمز من الهيدر
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  // التحقق من وجود الرمز
  if (!token) {
    throw createError(401, 'يرجى تسجيل الدخول للوصول إلى هذا المورد', 'غير مصرح');
  }

  try {
    // التحقق من صحة الرمز
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // الحصول على المستخدم
    const user = await User.findByPk(decoded.id, {
      attributes: { exclude: ['password', 'otp_code', 'otp_expires_at'] }
    });

    if (!user) {
      throw createError(401, 'المستخدم غير موجود', 'غير مصرح');
    }

    if (!user.is_active) {
      throw createError(401, 'الحساب غير نشط', 'حساب معطل');
    }

    // إضافة المستخدم إلى الطلب
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      throw createError(401, 'رمز المصادقة غير صحيح', 'غير مصرح');
    }
    if (error.name === 'TokenExpiredError') {
      throw createError(401, 'انتهت صلاحية رمز المصادقة', 'انتهت الصلاحية');
    }
    throw error;
  }
});

// التحقق من نوع المستخدم
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      throw createError(401, 'يرجى تسجيل الدخول أولاً', 'غير مصرح');
    }

    if (!roles.includes(req.user.user_type)) {
      throw createError(403, 'ليس لديك صلاحية للوصول إلى هذا المورد', 'ممنوع');
    }

    next();
  };
};

// التحقق من أن المستخدم مواطن
const citizenOnly = authorize('citizen');

// التحقق من أن المستخدم موظف أو إداري
const employeeOnly = authorize('employee', 'admin');

// التحقق من أن المستخدم إداري فقط
const adminOnly = authorize('admin');

// التحقق من أن المستخدم يمكنه الوصول إلى الطلب
const canAccessRequest = asyncHandler(async (req, res, next) => {
  const { Request } = require('../models');
  const requestId = req.params.id || req.params.requestId;

  if (!requestId) {
    throw createError(400, 'معرف الطلب مطلوب', 'بيانات ناقصة');
  }

  const request = await Request.findByPk(requestId);

  if (!request) {
    throw createError(404, 'الطلب غير موجود', 'غير موجود');
  }

  // المواطن يمكنه الوصول إلى طلباته فقط
  if (req.user.user_type === 'citizen' && request.citizen_id !== req.user.id) {
    throw createError(403, 'ليس لديك صلاحية للوصول إلى هذا الطلب', 'ممنوع');
  }

  // الموظف يمكنه الوصول إلى الطلبات المسندة إليه أو جميع الطلبات
  if (req.user.user_type === 'employee' && request.assigned_to && request.assigned_to !== req.user.id) {
    // يمكن للموظف رؤية جميع الطلبات ولكن تعديل المسندة إليه فقط
    if (['PUT', 'PATCH', 'DELETE'].includes(req.method)) {
      throw createError(403, 'يمكنك تعديل الطلبات المسندة إليك فقط', 'ممنوع');
    }
  }

  // الإداري يمكنه الوصول إلى جميع الطلبات
  // لا حاجة لفحص إضافي

  req.request = request;
  next();
});

// التحقق من أن المستخدم يمكنه الوصول إلى الإشعارات
const canAccessNotification = asyncHandler(async (req, res, next) => {
  const { Notification } = require('../models');
  const notificationId = req.params.id || req.params.notificationId;

  if (!notificationId) {
    throw createError(400, 'معرف الإشعار مطلوب', 'بيانات ناقصة');
  }

  const notification = await Notification.findByPk(notificationId);

  if (!notification) {
    throw createError(404, 'الإشعار غير موجود', 'غير موجود');
  }

  // المواطن يمكنه الوصول إلى إشعاراته فقط
  if (req.user.user_type === 'citizen' && notification.user_id !== req.user.id) {
    throw createError(403, 'ليس لديك صلاحية للوصول إلى هذا الإشعار', 'ممنوع');
  }

  // الموظف والإداري يمكنهما الوصول إلى جميع الإشعارات
  req.notification = notification;
  next();
});

// التحقق من التحقق من الهاتف
const requirePhoneVerification = (req, res, next) => {
  if (!req.user.is_verified) {
    throw createError(403, 'يجب التحقق من رقم الهاتف أولاً', 'غير محقق');
  }
  next();
};

// وسيط اختياري للمصادقة (لا يرمي خطأ إذا لم يكن هناك رمز)
const optionalAuth = asyncHandler(async (req, res, next) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findByPk(decoded.id, {
        attributes: { exclude: ['password', 'otp_code', 'otp_expires_at'] }
      });

      if (user && user.is_active) {
        req.user = user;
      }
    } catch (error) {
      // تجاهل الأخطاء في المصادقة الاختيارية
    }
  }

  next();
});

module.exports = {
  authenticate,
  authorize,
  citizenOnly,
  employeeOnly,
  adminOnly,
  canAccessRequest,
  canAccessNotification,
  requirePhoneVerification,
  optionalAuth
};
