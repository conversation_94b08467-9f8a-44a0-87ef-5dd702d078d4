import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { 
  UserIcon, 
  PhoneIcon, 
  EnvelopeIcon, 
  IdentificationIcon,
  MapPinIcon,
  CalendarIcon 
} from '@heroicons/react/24/outline';

const Register = () => {
  const navigate = useNavigate();
  const { register, isLoading, error, clearError, isAuthenticated } = useAuth();

  // بيانات النموذج
  const [formData, setFormData] = useState({
    fullName: '',
    phoneNumber: '',
    email: '',
    nationalId: '',
    address: '',
    dateOfBirth: '',
  });

  // حالة التحقق من صحة البيانات
  const [validation, setValidation] = useState({});

  // إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // مسح الأخطاء عند تغيير البيانات
  useEffect(() => {
    clearError();
  }, [formData, clearError]);

  // التحقق من صحة البيانات
  const validateForm = () => {
    const errors = {};

    // التحقق من الاسم الكامل
    if (!formData.fullName.trim()) {
      errors.fullName = 'الاسم الكامل مطلوب';
    } else if (formData.fullName.trim().length < 2) {
      errors.fullName = 'الاسم يجب أن يكون حرفين على الأقل';
    }

    // التحقق من رقم الهاتف
    if (!formData.phoneNumber.trim()) {
      errors.phoneNumber = 'رقم الهاتف مطلوب';
    } else if (!/^05\d{8}$/.test(formData.phoneNumber)) {
      errors.phoneNumber = 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 05 ويتكون من 10 أرقام)';
    }

    // التحقق من البريد الإلكتروني (اختياري)
    if (formData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'البريد الإلكتروني غير صحيح';
    }

    // التحقق من رقم الهوية (اختياري)
    if (formData.nationalId.trim() && !/^\d{10}$/.test(formData.nationalId)) {
      errors.nationalId = 'رقم الهوية يجب أن يتكون من 10 أرقام';
    }

    // التحقق من تاريخ الميلاد (اختياري)
    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 16 || age > 100) {
        errors.dateOfBirth = 'العمر يجب أن يكون بين 16 و 100 سنة';
      }
    }

    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  // التعامل مع تغيير البيانات
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // مسح خطأ الحقل عند التعديل
    if (validation[name]) {
      setValidation(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // التعامل مع إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();
    clearError();

    if (!validateForm()) {
      return;
    }

    // تحضير البيانات للإرسال
    const registrationData = {
      full_name: formData.fullName,
      phone_number: formData.phoneNumber,
      email: formData.email || undefined,
      national_id: formData.nationalId || undefined,
      address: formData.address || undefined,
      date_of_birth: formData.dateOfBirth || undefined,
    };

    const result = await register(registrationData);
    if (result.success) {
      // إعادة توجيه إلى صفحة تسجيل الدخول مع رسالة نجاح
      navigate('/login', { 
        state: { 
          message: 'تم إنشاء الحساب بنجاح! يرجى تسجيل الدخول باستخدام رقم الهاتف.',
          phoneNumber: formData.phoneNumber
        }
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        {/* الشعار والعنوان */}
        <div className="text-center mb-8">
          <div className="mx-auto h-16 w-16 bg-primary-600 rounded-full flex items-center justify-center">
            <UserIcon className="h-8 w-8 text-white" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            إنشاء حساب جديد
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            انضم إلى منصة المواطن وابدأ في تقديم طلباتك
          </p>
        </div>

        {/* عرض الأخطاء */}
        {error && (
          <div className="alert alert-error mb-6">
            <p>{error}</p>
          </div>
        )}

        {/* نموذج التسجيل */}
        <div className="card">
          <div className="card-body">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* الاسم الكامل */}
              <div>
                <label htmlFor="fullName" className="form-label">
                  الاسم الكامل *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <UserIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="fullName"
                    name="fullName"
                    type="text"
                    required
                    className={`form-input pr-10 ${validation.fullName ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="الاسم الكامل"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    disabled={isLoading}
                  />
                </div>
                {validation.fullName && (
                  <p className="mt-1 text-sm text-red-600">{validation.fullName}</p>
                )}
              </div>

              {/* رقم الهاتف */}
              <div>
                <label htmlFor="phoneNumber" className="form-label">
                  رقم الهاتف *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <PhoneIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="phoneNumber"
                    name="phoneNumber"
                    type="tel"
                    required
                    className={`form-input pr-10 ${validation.phoneNumber ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="05xxxxxxxx"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    disabled={isLoading}
                  />
                </div>
                {validation.phoneNumber && (
                  <p className="mt-1 text-sm text-red-600">{validation.phoneNumber}</p>
                )}
              </div>

              {/* البريد الإلكتروني */}
              <div>
                <label htmlFor="email" className="form-label">
                  البريد الإلكتروني (اختياري)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    className={`form-input pr-10 ${validation.email ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={isLoading}
                  />
                </div>
                {validation.email && (
                  <p className="mt-1 text-sm text-red-600">{validation.email}</p>
                )}
              </div>

              {/* رقم الهوية */}
              <div>
                <label htmlFor="nationalId" className="form-label">
                  رقم الهوية الوطنية (اختياري)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <IdentificationIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="nationalId"
                    name="nationalId"
                    type="text"
                    maxLength="10"
                    className={`form-input pr-10 ${validation.nationalId ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    placeholder="1234567890"
                    value={formData.nationalId}
                    onChange={handleInputChange}
                    disabled={isLoading}
                  />
                </div>
                {validation.nationalId && (
                  <p className="mt-1 text-sm text-red-600">{validation.nationalId}</p>
                )}
              </div>

              {/* العنوان */}
              <div>
                <label htmlFor="address" className="form-label">
                  العنوان (اختياري)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <MapPinIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <textarea
                    id="address"
                    name="address"
                    rows="3"
                    className="form-input pr-10"
                    placeholder="العنوان الكامل"
                    value={formData.address}
                    onChange={handleInputChange}
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* تاريخ الميلاد */}
              <div>
                <label htmlFor="dateOfBirth" className="form-label">
                  تاريخ الميلاد (اختياري)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <CalendarIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="dateOfBirth"
                    name="dateOfBirth"
                    type="date"
                    className={`form-input pr-10 ${validation.dateOfBirth ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                    value={formData.dateOfBirth}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    max={new Date(new Date().setFullYear(new Date().getFullYear() - 16)).toISOString().split('T')[0]}
                  />
                </div>
                {validation.dateOfBirth && (
                  <p className="mt-1 text-sm text-red-600">{validation.dateOfBirth}</p>
                )}
              </div>

              {/* زر التسجيل */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'جاري إنشاء الحساب...' : 'إنشاء الحساب'}
              </button>
            </form>
          </div>
        </div>

        {/* رابط تسجيل الدخول */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-600">
            لديك حساب بالفعل؟{' '}
            <Link to="/login" className="font-medium text-primary-600 hover:text-primary-500">
              تسجيل الدخول
            </Link>
          </p>
        </div>

        {/* ملاحظة */}
        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>ملاحظة:</strong> الحقول المميزة بعلامة (*) مطلوبة. بعد إنشاء الحساب، ستحتاج إلى التحقق من رقم الهاتف عبر رمز OTP.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Register;
