{"name": "citizen-platform-backend", "version": "1.0.0", "description": "Backend API for Citizen Platform - نظام إدارة طلبات المواطنين", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "migrate": "node src/utils/migrate.js", "seed": "node src/utils/seed.js"}, "keywords": ["citizen", "platform", "government", "requests", "api"], "author": "Citizen Platform Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.32.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "twilio": "^4.14.0", "firebase-admin": "^11.10.1", "joi": "^17.9.2", "express-rate-limit": "^6.8.1", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^9.0.0", "moment": "^2.29.4", "pdf-lib": "^1.17.1", "sharp": "^0.32.4"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "@types/jest": "^29.5.3"}, "engines": {"node": ">=18.0.0"}}