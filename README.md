# منصة المواطن - نظام إدارة الطلبات

## وصف المشروع

منصة متكاملة لإدارة طلبات المواطنين تتكون من:

### 1. منصة المواطن
- تسجيل الدخول برقم الهاتف (OTP)
- تقديم طلب جديد مع إمكانية رفع صور
- متابعة حالة الطلب (قيد المعالجة، جاري الإنجاز، مكتمل)
- استلام إشعارات مباشرة

### 2. منصة المكتب (الإدارة)
- تسجيل الدخول للموظفين
- استقبال الطلبات الجديدة
- تحديث حالة الطلب
- إضافة ملاحظات ومرفقات
- إنشاء تقارير شهرية وسنوية
- إدارة الإشعارات

### 3. قاعدة البيانات المشتركة
- تخزين بيانات المواطنين والطلبات
- إدارة الصور والمرفقات
- تخزين التقارير والإشعارات

## التقنيات المستخدمة

- **Frontend**: React + Vite + Tailwind CSS
- **Backend**: Node.js + Express
- **Database**: PostgreSQL
- **Authentication**: JWT + OTP
- **Notifications**: Firebase Cloud Messaging
- **File Storage**: Local storage (قابل للترقية إلى AWS S3)

## هيكل المشروع

```
citizen-platform/
├── backend/                 # خادم Node.js
│   ├── src/
│   │   ├── controllers/     # منطق التحكم
│   │   ├── models/          # نماذج قاعدة البيانات
│   │   ├── routes/          # مسارات API
│   │   ├── middleware/      # الوسطاء
│   │   ├── services/        # الخدمات
│   │   └── utils/           # الأدوات المساعدة
│   ├── uploads/             # ملفات المرفوعة
│   └── package.json
├── citizen-frontend/        # واجهة المواطن
│   ├── src/
│   │   ├── components/      # المكونات
│   │   ├── pages/           # الصفحات
│   │   ├── services/        # خدمات API
│   │   └── utils/           # الأدوات
│   └── package.json
├── admin-frontend/          # واجهة الإدارة
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   └── utils/
│   └── package.json
└── database/                # ملفات قاعدة البيانات
    ├── migrations/          # ملفات الترحيل
    └── seeds/               # البيانات الأولية
```

## التشغيل

### متطلبات النظام
- Node.js (v18 أو أحدث)
- PostgreSQL
- npm أو yarn

### خطوات التشغيل

1. **إعداد قاعدة البيانات**
```bash
# إنشاء قاعدة البيانات
createdb citizen_platform
```

2. **تشغيل الخادم**
```bash
cd backend
npm install
npm run dev
```

3. **تشغيل واجهة المواطن**
```bash
cd citizen-frontend
npm install
npm run dev
```

4. **تشغيل واجهة الإدارة**
```bash
cd admin-frontend
npm install
npm run dev
```

## المميزات

- ✅ واجهة سهلة الاستخدام
- ✅ نظام إشعارات فوري
- ✅ تقارير تلقائية
- ✅ رفع وإدارة المرفقات
- ✅ متابعة حالة الطلبات
- ✅ لوحة تحكم شاملة للإدارة

## الحالة الحالية

🚧 المشروع قيد التطوير

## المساهمة

هذا مشروع مفتوح المصدر، مرحب بالمساهمات والتحسينات.
