import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { PhoneIcon, KeyIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

const Login = () => {
  const navigate = useNavigate();
  const { login, sendOTP, verifyOTP, isLoading, error, clearError, isAuthenticated } = useAuth();

  // حالات النموذج
  const [loginType, setLoginType] = useState('citizen'); // citizen أو employee
  const [step, setStep] = useState(1); // 1: إدخال البيانات، 2: التحقق من OTP
  const [showPassword, setShowPassword] = useState(false);
  
  // بيانات المواطن
  const [citizenData, setCitizenData] = useState({
    phoneNumber: '',
    otpCode: '',
  });

  // بيانات الموظف
  const [employeeData, setEmployeeData] = useState({
    email: '',
    password: '',
  });

  // حالة العد التنازلي لإعادة إرسال OTP
  const [countdown, setCountdown] = useState(0);

  // إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // العد التنازلي لإعادة إرسال OTP
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // مسح الأخطاء عند تغيير نوع تسجيل الدخول
  useEffect(() => {
    clearError();
    setStep(1);
    setCitizenData({ phoneNumber: '', otpCode: '' });
    setEmployeeData({ email: '', password: '' });
  }, [loginType, clearError]);

  // التعامل مع تسجيل دخول المواطن - الخطوة الأولى
  const handleCitizenLogin = async (e) => {
    e.preventDefault();
    clearError();

    if (!citizenData.phoneNumber.trim()) {
      return;
    }

    const result = await sendOTP(citizenData.phoneNumber);
    if (result.success) {
      setStep(2);
      setCountdown(300); // 5 دقائق
    }
  };

  // التعامل مع تسجيل دخول المواطن - التحقق من OTP
  const handleOTPVerification = async (e) => {
    e.preventDefault();
    clearError();

    if (!citizenData.otpCode.trim()) {
      return;
    }

    const result = await verifyOTP(citizenData.phoneNumber, citizenData.otpCode);
    if (result.success) {
      navigate('/dashboard');
    }
  };

  // التعامل مع تسجيل دخول الموظف
  const handleEmployeeLogin = async (e) => {
    e.preventDefault();
    clearError();

    if (!employeeData.email.trim() || !employeeData.password.trim()) {
      return;
    }

    const result = await login(employeeData.email, employeeData.password);
    if (result.success) {
      navigate('/admin');
    }
  };

  // إعادة إرسال OTP
  const handleResendOTP = async () => {
    if (countdown > 0) return;

    const result = await sendOTP(citizenData.phoneNumber);
    if (result.success) {
      setCountdown(300);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* الشعار والعنوان */}
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-primary-600 rounded-full flex items-center justify-center">
            <svg className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            منصة المواطن
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            تسجيل الدخول إلى حسابك
          </p>
        </div>

        {/* اختيار نوع تسجيل الدخول */}
        <div className="flex rounded-lg bg-gray-100 p-1">
          <button
            type="button"
            onClick={() => setLoginType('citizen')}
            className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
              loginType === 'citizen'
                ? 'bg-white text-primary-600 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            مواطن
          </button>
          <button
            type="button"
            onClick={() => setLoginType('employee')}
            className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
              loginType === 'employee'
                ? 'bg-white text-primary-600 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            موظف
          </button>
        </div>

        {/* عرض الأخطاء */}
        {error && (
          <div className="alert alert-error">
            <p>{error}</p>
          </div>
        )}

        {/* نموذج تسجيل دخول المواطن */}
        {loginType === 'citizen' && (
          <div className="card">
            <div className="card-body">
              {step === 1 ? (
                <form onSubmit={handleCitizenLogin} className="space-y-6">
                  <div>
                    <label htmlFor="phoneNumber" className="form-label">
                      رقم الهاتف
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <PhoneIcon className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        id="phoneNumber"
                        name="phoneNumber"
                        type="tel"
                        required
                        className="form-input pr-10"
                        placeholder="05xxxxxxxx"
                        value={citizenData.phoneNumber}
                        onChange={(e) => setCitizenData({ ...citizenData, phoneNumber: e.target.value })}
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <button
                    type="submit"
                    disabled={isLoading || !citizenData.phoneNumber.trim()}
                    className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? 'جاري الإرسال...' : 'إرسال رمز التحقق'}
                  </button>
                </form>
              ) : (
                <form onSubmit={handleOTPVerification} className="space-y-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-4">
                      تم إرسال رمز التحقق إلى رقم الهاتف
                    </p>
                    <p className="font-medium text-primary-600">
                      {citizenData.phoneNumber}
                    </p>
                  </div>

                  <div>
                    <label htmlFor="otpCode" className="form-label">
                      رمز التحقق
                    </label>
                    <input
                      id="otpCode"
                      name="otpCode"
                      type="text"
                      required
                      maxLength="6"
                      className="form-input text-center text-lg tracking-widest"
                      placeholder="000000"
                      value={citizenData.otpCode}
                      onChange={(e) => setCitizenData({ ...citizenData, otpCode: e.target.value.replace(/\D/g, '') })}
                      disabled={isLoading}
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={isLoading || citizenData.otpCode.length !== 6}
                    className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? 'جاري التحقق...' : 'تسجيل الدخول'}
                  </button>

                  <div className="text-center">
                    {countdown > 0 ? (
                      <p className="text-sm text-gray-500">
                        يمكنك إعادة الإرسال بعد {Math.floor(countdown / 60)}:{(countdown % 60).toString().padStart(2, '0')}
                      </p>
                    ) : (
                      <button
                        type="button"
                        onClick={handleResendOTP}
                        className="text-sm text-primary-600 hover:text-primary-500"
                        disabled={isLoading}
                      >
                        إعادة إرسال رمز التحقق
                      </button>
                    )}
                  </div>

                  <button
                    type="button"
                    onClick={() => setStep(1)}
                    className="w-full btn-secondary"
                  >
                    العودة
                  </button>
                </form>
              )}
            </div>
          </div>
        )}

        {/* نموذج تسجيل دخول الموظف */}
        {loginType === 'employee' && (
          <div className="card">
            <div className="card-body">
              <form onSubmit={handleEmployeeLogin} className="space-y-6">
                <div>
                  <label htmlFor="email" className="form-label">
                    البريد الإلكتروني
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    className="form-input"
                    placeholder="<EMAIL>"
                    value={employeeData.email}
                    onChange={(e) => setEmployeeData({ ...employeeData, email: e.target.value })}
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <label htmlFor="password" className="form-label">
                    كلمة المرور
                  </label>
                  <div className="relative">
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      required
                      className="form-input pl-10"
                      placeholder="كلمة المرور"
                      value={employeeData.password}
                      onChange={(e) => setEmployeeData({ ...employeeData, password: e.target.value })}
                      disabled={isLoading}
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 left-0 pl-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={isLoading || !employeeData.email.trim() || !employeeData.password.trim()}
                  className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                </button>
              </form>
            </div>
          </div>
        )}

        {/* رابط التسجيل للمواطنين */}
        {loginType === 'citizen' && step === 1 && (
          <div className="text-center">
            <p className="text-sm text-gray-600">
              ليس لديك حساب؟{' '}
              <Link to="/register" className="font-medium text-primary-600 hover:text-primary-500">
                إنشاء حساب جديد
              </Link>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Login;
